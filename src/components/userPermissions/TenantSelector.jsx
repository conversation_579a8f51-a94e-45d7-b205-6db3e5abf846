import React from 'react';
import { Controller } from 'react-hook-form';
import Stack from '@mui/material/Stack';
import FormHelperText from '@mui/material/FormHelperText';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const TenantSelector = ({ control, tenantsList, tenantId, setTenantId, editOrgId, errors, label = 'Name', placeholder = 'Select NGO' }) => {
  return (
    <Stack spacing={1}>
      <CustomInputLabel>
        {label} <span style={{ color: 'red' }}>*</span>
      </CustomInputLabel>
      <Controller
        name="tenantId"
        control={control}
        render={({ field }) => (
          <CustomDropdownField
            id="tenantId"
            name="tenantId"
            control={control}
            options={tenantsList}
            value={tenantId}
            defaultValue={editOrgId}
            placeholder={placeholder}
            onChange={(event) => {
              field.onChange(event.target?.value);
              setTenantId(event.target?.value);
            }}
            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
          />
        )}
      />
      {errors?.tenantId && <FormHelperText sx={{ color: 'error.main' }}>{errors.tenantId.message}</FormHelperText>}
    </Stack>
  );
};

export default TenantSelector;


