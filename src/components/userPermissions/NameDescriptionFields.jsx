import React from 'react';
import Stack from '@mui/material/Stack';
import Grid from '@mui/material/Grid';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';

const NameDescriptionFields = ({
  control,
  nameDefault = '',
  descDefault = '',
  nameLabel = 'Role Name',
  descLabel = 'Description',
  asGridItems = false,
  nameGridProps = { xs: 12, sm: 4 },
  descGridProps = { xs: 12, sm: 4 }
}) => {
  const nameField = (
    <Stack spacing={1}>
      <CustomInputLabel>
        {nameLabel} <span style={{ color: 'red' }}>*</span>
      </CustomInputLabel>
      <CustomNameField
        name="name"
        control={control}
        placeholder={`Enter ${nameLabel}`}
        defaultValue={nameDefault}
        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
      />
    </Stack>
  );

  const descField = (
    <Stack spacing={1}>
      <CustomInputLabel>
        {descLabel} <span style={{ color: 'red' }}>*</span>
      </CustomInputLabel>
      <CustomAllCharactersField
        name="description"
        control={control}
        placeholder="Enter description"
        defaultValue={descDefault}
        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
      />
    </Stack>
  );

  if (asGridItems) {
    return (
      <>
        <Grid item {...nameGridProps}>
          {nameField}
        </Grid>
        <Grid item {...descGridProps}>
          {descField}
        </Grid>
      </>
    );
  }

  return (
    <>
      {nameField}
      {descField}
    </>
  );
};

export default NameDescriptionFields;
