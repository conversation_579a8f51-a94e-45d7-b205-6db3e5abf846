import React, { useState } from 'react';
import PropTypes from 'prop-types';
import IconButton from '@mui/material/IconButton';
import { Menu, MenuItem } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const ActionsCellBase = ({ row, navigate, handleDeleteClick, editPath, resolvePayload }) => {
	const [anchorEl, setAnchorEl] = useState(null);
	const open = Boolean(anchorEl);

	const handleMenuClick = (event) => {
		event.stopPropagation();
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const handleEdit = () => {
		const payload = resolvePayload ? resolvePayload(row) : row?.original;
		navigate(editPath, {
			state: { data: JSON.parse(JSON.stringify(payload)) }
		});
	};

	return (
		<>
			<IconButton onClick={handleMenuClick}>
				<MoreVertIcon />
			</IconButton>
			<Menu
				anchorEl={anchorEl}
				open={open}
				onClose={handleClose}
				anchorOrigin={{
					vertical: 'bottom',
					horizontal: 'right'
				}}
				transformOrigin={{
					vertical: 'top',
					horizontal: 'right'
				}}
			>
				<MenuItem onClick={handleEdit}>Edit</MenuItem>
				<MenuItem onClick={() => handleDeleteClick(row?.original)}>Delete</MenuItem>
			</Menu>
		</>
	);
};

ActionsCellBase.propTypes = {
	row: PropTypes.object.isRequired,
	navigate: PropTypes.func.isRequired,
	handleDeleteClick: PropTypes.func.isRequired,
	editPath: PropTypes.string.isRequired,
	resolvePayload: PropTypes.func
};

export default ActionsCellBase;


