import React from 'react';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import FormHelperText from '@mui/material/FormHelperText';

const EmailInput = ({ id = 'email', label = 'Email Address', name = 'email', value, error, touched, onChange, onBlur, placeholder = 'Enter email address' }) => {
  return (
    <>
      <Stack spacing={1}>
        <InputLabel htmlFor={id}>{label}</InputLabel>
        <OutlinedInput
          size="small"
          fullWidth
          error={Boolean(touched && error)}
          id={id}
          type="email"
          value={value}
          name={name}
          onBlur={onBlur}
          onChange={onChange}
          placeholder={placeholder}
          inputProps={{}}
        />
      </Stack>
      {touched && error && (
        <FormHelperText error id={`helper-text-${id}`}>
          {error}
        </FormHelperText>
      )}
    </>
  );
};

export default EmailInput;


