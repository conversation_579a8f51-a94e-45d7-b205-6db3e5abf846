import React, { useState } from 'react';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import FormHelperText from '@mui/material/FormHelperText';
import IconButton from 'components/@extended/IconButton';
import { Eye, EyeSlash } from 'iconsax-react';

const PasswordInput = ({
  id,
  label,
  name,
  value,
  error,
  touched,
  onChange,
  onBlur,
  placeholder,
  withToggle = true
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const handleToggle = () => setShowPassword((prev) => !prev);
  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <>
      <Stack spacing={1}>
        <InputLabel htmlFor={id}>{label}</InputLabel>
        <OutlinedInput
          size="small"
          fullWidth
          error={Boolean(touched && error)}
          id={id}
          type={withToggle && showPassword ? 'text' : 'password'}
          value={value}
          name={name}
          onBlur={onBlur}
          onChange={onChange}
          endAdornment={
            withToggle ? (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleToggle}
                  onMouseDown={handleMouseDownPassword}
                  edge="end"
                  color="secondary"
                >
                  {showPassword ? <Eye /> : <EyeSlash />}
                </IconButton>
              </InputAdornment>
            ) : undefined
          }
          placeholder={placeholder}
        />
      </Stack>
      {touched && error && (
        <FormHelperText error id={`helper-text-${id}`}>
          {error}
        </FormHelperText>
      )}
    </>
  );
};

export default PasswordInput;


