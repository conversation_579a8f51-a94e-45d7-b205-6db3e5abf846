import { Link as RouterLink, useNavigate } from 'react-router-dom';

// material-ui
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import AnimateButton from 'components/@extended/AnimateButton';

export default function AuthLogout() {
  const navigate = useNavigate();
  // No menu needed anymore per requirement; direct navigation handled on button

  return (
    <Grid container spacing={{ xs: 2, sm: 3 }}>
      <Grid item xs={12}>
        <Typography variant="h5" color="primary" gutterBottom>
          Successfully logged out 👋🏻
        </Typography>
      </Grid>

      <Grid item xs={12} sx={{ mt: { xs: 1, sm: 0 } }}>
        <AnimateButton>
          <Button
            component={RouterLink}
            to="/login"
            disableElevation
            fullWidth
            size="small"
            type="button"
            variant="contained"
            sx={{
              backgroundColor: 'primary.main',
              '&:hover': { backgroundColor: 'primary.main' }
            }}
          >
            Login
          </Button>
        </AnimateButton>
      </Grid>

      <Grid item xs={12}>
        <Button component={RouterLink} to="/register" variant="outlined" color="primary" fullWidth size="small">
          Create New Account
        </Button>
      </Grid>
    </Grid>
  );
}
