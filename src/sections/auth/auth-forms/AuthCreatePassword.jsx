import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// material-ui
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import axios from 'axios';

// third-party
import * as Yup from 'yup';
import { Formik } from 'formik';

// project-imports
import useScriptRef from 'hooks/useScriptRef';
import AnimateButton from 'components/@extended/AnimateButton';
import PasswordInput from '../components/PasswordInput';
import { openSnackbar } from 'api/snackbar';


// ============================|| CREATE PASSWORD ||============================ //

const API_URL = import.meta.env.VITE_APP_API_URL;

export default function AuthCreatePassword() {
  const scriptedRef = useScriptRef();
  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleClickShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const createPassword = async (newPassword) => {
    try {
      await axios.post(`${API_URL}/api/create-password`, {
        newPassword,
      });
  
      openSnackbar({
        open: true,
        message: "Password has been created successfully.",
        variant: "alert",
        alert: { color: "success" },
      });
  
      setTimeout(() => {
        navigate("/login", { replace: true }); // Redirect to login page
      }, 1500);
    } catch (error) {
      console.error("Error creating password:", error);
      throw error;
    }
  };

  return (
    <Formik
        initialValues={{
          password: '',
          confirmPassword: '',
          submit: null
        }}
        validationSchema={Yup.object().shape({
          password: Yup.string()
            .min(8, 'Password must be at least 8 characters')
            .max(255)
            .required('Password is required'),
          confirmPassword: Yup.string()
            .required('Confirm Password is required')
            .test('confirmPassword', 'Both Password must be match!', (confirmPassword, yup) => yup.parent.password === confirmPassword)
        })}
        onSubmit={async (values, { setErrors, setStatus, setSubmitting }) => {
          try {
            await createPassword(values.password);
            if (scriptedRef.current) {
              setStatus({ success: true });
              setSubmitting(false);
            }
          } catch (err) {
            console.error(err);
            if (scriptedRef.current) {
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }
        }}
      >
        {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values }) => (
          <form noValidate onSubmit={handleSubmit}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12}>
                <PasswordInput
                  id="password-create"
                  label="Password"
                  name="password"
                  value={values.password}
                  error={errors.password}
                  touched={touched.password}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  placeholder="Enter password"
                  withToggle
                />
              </Grid>
              <Grid item xs={12}>
                <PasswordInput
                  id="confirm-password-create"
                  label="Confirm Password"
                  name="confirmPassword"
                  value={values.confirmPassword}
                  error={errors.confirmPassword}
                  touched={touched.confirmPassword}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  placeholder="Enter confirm password"
                  withToggle
                />
              </Grid>
              
              <Grid item xs={12}>
                <AnimateButton>
                  <Button disableElevation disabled={isSubmitting} fullWidth size="small" type="submit" variant="contained" color="primary">
                    Create Password
                  </Button>
                </AnimateButton>
              </Grid>
            </Grid>
          </form>
        )}
    </Formik>
  );
}
