import PropTypes from 'prop-types';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

import { useMemo } from 'react';
import { useForm } from 'react-hook-form';

import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';

export default function ProfileDialog({ open, onClose, userName, user, onSave }) {
  console.log("this is user", user)
  const defaultFirst = useMemo(() => (user?.first_name ? user.first_name : userName ? userName.split(' ')[0] : ''), [user, userName]);
  const defaultLast = useMemo(
    () => (user?.last_name ? user.last_name : userName ? userName.split(' ').slice(1).join(' ') || '' : ''),
    [user, userName]
  );

  const { control, handleSubmit } = useForm({
    defaultValues: {
      firstName: defaultFirst,
      lastName: defaultLast,
      mobileNumber: user?.phone || user?.mobile || ''
    }
  });

  const submit = handleSubmit((values) => {
    if (typeof onSave === 'function') onSave(values);
    onClose?.();
  });

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="xs">
      <DialogTitle sx={{ m: 0, p: 2 }}>
        Profile
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <CustomInputLabel htmlFor="firstName">First Name</CustomInputLabel>
            <CustomNameField
              name="firstName"
              control={control}
              placeholder="Enter First Name"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomInputLabel htmlFor="lastName">Last Name</CustomInputLabel>
            <CustomNameField
              name="lastName"
              control={control}
              placeholder="Enter Last Name"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomInputLabel htmlFor="mobileNumber">Mobile Number</CustomInputLabel>
            <CustomContactNumberField name="mobileNumber" control={control} placeholder="Mobile Number" />
          </Grid>
          <Grid item xs={12}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Email:
              </Typography>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {user?.email || ''}
              </Typography>
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" size="small" onClick={onClose}>
          Cancel
        </Button>
        <Button variant="contained" size="small" onClick={submit} color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ProfileDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  userName: PropTypes.string,
  user: PropTypes.object,
  onSave: PropTypes.func
};
