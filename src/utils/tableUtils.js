import { compareItems } from '@tanstack/match-sorter-utils';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = require('@tanstack/match-sorter-utils').rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

export const fuzzySort = (rowA, rowB, columnId) => {
  let dir = 0;
  if (rowA.columnFiltersMeta[columnId]) {
    dir = compareItems(rowA.columnFiltersMeta[columnId], rowB.columnFiltersMeta[columnId]);
  }
  return dir === 0 ? require('@tanstack/table-core').sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
};

export const buildHeaders = (table) => {
  const headers = [];
  table.getVisibleLeafColumns().forEach((columns) => {
    if (columns.columnDef.accessorKey) {
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        key: columns.columnDef.accessorKey
      });
    }
  });
  return headers;
};
