import Grid from '@mui/material/Grid';

import Logo from 'components/logo';
import AuthWrapper from 'sections/auth/AuthWrapper';
import AuthLogout from 'sections/auth/auth-forms/AuthLogout';

export default function Logout() {
  return (
    <AuthWrapper>
      <Grid container spacing={3}>
        <Grid item xs={12} sx={{ textAlign: 'center' }}>
          <Logo sx={{ pointerEvents: 'none' }} />
        </Grid>
        <Grid item xs={12}>
          <AuthLogout />
        </Grid>
      </Grid>
    </AuthWrapper>
  );
}


