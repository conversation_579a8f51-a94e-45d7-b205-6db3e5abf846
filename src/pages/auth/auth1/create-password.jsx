// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project-imports
import AuthWrapper from 'sections/auth/AuthWrapper';
import AuthCreatePassword from 'sections/auth/auth-forms/AuthCreatePassword';

// ================================|| CREATE PASSWORD ||================================ //

export default function CreatePassword() {
  return (
    <AuthWrapper>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Stack sx={{ mb: { xs: -0.5, sm: 0.5 } }} spacing={1}>
            <Typography variant="h3">Create Password</Typography>
            <Typography color="secondary">Please create your new password</Typography>
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <AuthCreatePassword />
        </Grid>
      </Grid>
    </AuthWrapper>
  );
}
