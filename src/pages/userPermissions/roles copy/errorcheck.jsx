import React, { useState} from 'react';
import { useForm, Controller} from 'react-hook-form';
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import { borderRadius, Box } from '@mui/system';
import { Tab, Checkbox } from '@mui/material';
import { Link } from 'react-router-dom';
import IconButton from '@mui/material/IconButton';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import { useNavigate } from 'react-router-dom';
import { TabContext, TabList, TabPanel } from "@mui/lab";
// project-imports
import MainCard from 'components/MainCard';
import { useRef } from 'react';
import { Outlet } from 'react-router';
import ProfileTab from 'pages/userPermissions/roles/ProfileTab';
import data from './staticdata';
import CustomCheckbox from './checkbox';
import WorkIcon from "@mui/icons-material/Work";
import GroupIcon from "@mui/icons-material/Group";


import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,


} from "@mui/material";
import List from '@mui/material/List';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';

// const data = [
//   {
//     name: "User Management",
//     permissions: 15,ss
//     children: [
//       {
//         name: "Add User",
//         permissions: 7,
//         children: [],
//       },
//       {
//         name: "Edit User",
//         permissions: 5,
//         children: [],
//       },
//     ],
//   },
//   {
//     name: "Roles",
//     permissions: 0,
//     children: [
//       {
//         name: "Add Role",
//         permissions: 3,
//         children: [],
//       },
//       {
//         name: "Edit Role",
//         permissions: 6,
//         children: [],
//       },
//     ],
//   },
// ];


const PermissionItem = ({ item, level = 0, updatePermissions }) => {
  
  const [expanded, setExpanded] = useState(false);

  const hasChildren = item.children && item.children.length > 0;

  // Helper function to replace underscores with spaces in the name
  const formatName = (name) => name.replace(/_/g, " ");

  // Determine the initial permissions using bitwise operations
  const getPermissionState = (permissionValue) => ({
    read: (permissionValue & 1) !== 0,
    create: (permissionValue & 2) !== 0,
    update: (permissionValue & 4) !== 0,
    delete: (permissionValue & 8) !== 0,
  });

  const [permissions, setPermissions] = useState(
    getPermissionState(item.permissions)
  );

  // Check if "Select All" should be checked
  const isSelectAllChecked =
    permissions.read &&
    permissions.create &&
    permissions.update &&
    permissions.delete;

  // Handle individual permission toggle
  const handlePermissionChange = (permission) => {
    setPermissions((prevPermissions) => {
      const newPermissions = {
        ...prevPermissions,
        [permission]: !prevPermissions[permission],
      };

      // Calculate the updated permissions integer value
      const updatedPermissionsValue =
        (newPermissions.read ? 1 : 0) |
        (newPermissions.create ? 2 : 0) |
        (newPermissions.update ? 4 : 0) |
        (newPermissions.delete ? 8 : 0);

      // Update permissions in the main data tree
      updatePermissions(item.name, updatedPermissionsValue);

      return newPermissions;
    });
  };

  // Handle "Select All" toggle
  const handleSelectAllChange = () => {
    const newSelectAllState = !isSelectAllChecked;
    const newPermissions = {
      read: newSelectAllState,
      create: newSelectAllState,
      update: newSelectAllState,
      delete: newSelectAllState,
    };

    setPermissions(newPermissions);

    // Calculate the updated permissions integer value
    const updatedPermissionsValue =
      (newPermissions.read ? 1 : 0) |
      (newPermissions.create ? 2 : 0) |
      (newPermissions.update ? 4 : 0) |
      (newPermissions.delete ? 8 : 0);

    // Update permissions in the main data tree
    updatePermissions(item.name, updatedPermissionsValue);
  };

  const renderPermissions = () => (
    <div style={{ display: "flex", gap: "3rem", alignItems: "center" }}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "80px",
          gap: "0.5rem",
        }}
      >
        {level === 0 && <span>Read</span>}
        <Checkbox
          checked={permissions.read}
          onChange={() => handlePermissionChange("read")}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "80px",
          gap: "0.5rem",
        }}
      >
        {level === 0 && <span>Create</span>}
        <Checkbox
          checked={permissions.create}
          onChange={() => handlePermissionChange("create")}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "80px",
          gap: "0.5rem",
        }}
      >
        {level === 0 && <span>Update</span>}
        <Checkbox
          checked={permissions.update}
          onChange={() => handlePermissionChange("update")}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "80px",
          gap: "0.5rem",
        }}
      >
        {level === 0 && <span>Delete</span>}
        <Checkbox
          checked={permissions.delete}
          onChange={() => handlePermissionChange("delete")}
        />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "80px",
          gap: "0.5rem",
        }}
      >
        {level === 0 && <span>Select All</span>}
        <Checkbox
          checked={isSelectAllChecked}
          onChange={handleSelectAllChange}
        />
      </div>
    </div>
  );

  // Calculate dynamic font size based on level
  const fontSize = level <= 1 ? "1rem" : `${1 - (level - 1) * 0.1}rem`;
  const fontWeight = level === 0 ? "bold" : "normal";
 
  return (
    <div style={{ marginLeft: level * 10, marginTop: 10 }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          padding: "5px 10px",
          borderRadius: "6px",
          backgroundColor: "transparent",
          transition: "background-color 0.2s",
        }}
        onMouseEnter={(e) =>
          (e.currentTarget.style.backgroundColor = "#f2f2f2")
        }
        onMouseLeave={(e) =>
          (e.currentTarget.style.backgroundColor = "transparent")
        }
      >
        <span
          style={{
            cursor: "pointer",
            fontSize: fontSize,
            fontWeight: fontWeight,
          }}
          onClick={() => setExpanded(!expanded)}
        >
          {hasChildren &&
            (expanded ? (
              <ExpandLessIcon style={{ fontSize: "16px" }} />
            ) : (
              <ExpandMoreIcon style={{ fontSize: "16px" }} />
            ))}{" "}
          {formatName(item.name)}
        </span>
        <div style={{ display: "flex", marginLeft: "auto" }}>
          {renderPermissions()}
        </div>
      </div>
      {expanded &&
        hasChildren &&
        item.children
          .slice() // Copy to avoid mutation
          .sort((a, b) => a.displayNumber - b.displayNumber)
          .map((child) => (
            <PermissionItem
              key={child.name}
              item={child}
              level={level + 1}
              updatePermissions={updatePermissions}
            />
          ))}
    </div>
  );
};
const data1 = [
  {
    name: "Left_Menu",
    children: [
      {
        name: "Client",
        type: "PAGE",
        permissions: 15,
        displayNumber: 9,
        children: [
          { name: "Add_Client", type: "ACTION", permissions: 15, displayNumber: 1 },
          { name: "Edit", type: "ACTION", permissions: 15, displayNumber: 2 },
          { name: "Update", type: "ACTION", permissions: 15, displayNumber: 3 },
          { name: "Deactivate", type: "ACTION", permissions: 15, displayNumber: 4 },
        ],
      },
      {
        name: "Employee",
        type: "PAGE",
        permissions: 15,
        displayNumber: 10,
        children: [
          { name: "Add_Employee", type: "ACTION", permissions: 15, displayNumber: 1 },
          { name: "Edit_Employee", type: "ACTION", permissions: 15, displayNumber: 2 },
          { name: "Update_Employee", type: "ACTION", permissions: 15, displayNumber: 3 },
          { name: "Deactivate_Employee", type: "ACTION", permissions: 15, displayNumber: 4 },
        ],
      },
    ],
    permissions: 15,
    displayNumber: 1,
  },
];
// Permissions mapping
const permissionMapping = {
  Read: 1, // Binary 0001
  Write: 2, // Binary 0010
  Update: 4, // Binary 0100
  Delete: 8, // Binary 1000
  Select_All: 15, // Full permissions
};
const AddRoles = () => {
  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm();

  const leftMenuData = data1.find((menu) => menu.name === "Left_Menu")?.children;

  // Check if a permission is enabled
  const isPermissionEnabled = (permissions, permissionKey) => {
    const bitValue = permissionMapping[permissionKey];
    return (permissions & bitValue) === bitValue;
  };

  const [selectedMainTab, setSelectedMainTab] = useState("0");
  const [selectedSubTab, setSelectedSubTab] = useState({});
  const [checked, setChecked] = useState(false);

  const navigate = useNavigate(); 
  const inputRef = useRef(null);

  const [permissions, setPermissions] = useState([
    {
      category: "Jobs management",
      actions: [
        { name: "Create new job and stages", roles: { member: false, manager: true, admin: true } },
        { name: "Edit job and stages", roles: { member: false, manager: true, admin: true } },
        { name: "Archive jobs", roles: { member: false, manager: true, admin: true } },
        { name: "Change job status", roles: { member: false, manager: true, admin: true } },
      ],
    },
    {
      category: "Candidate management",
      actions: [
        { name: "Add candidates", roles: { member: true, manager: true, admin: true } },
        { name: "Edit candidates", roles: { member: true, manager: true, admin: true } },
      ],
    },
  ]);



  const handleMainTabChange = (event, newValue) => {
    setSelectedMainTab(newValue);

    // Reset the selected sub-tab for the newly selected main tab to "0"
    setSelectedSubTab((prev) => ({
      ...prev,
      [newValue]: "0",
    }));
  };

  const handleSubTabChange = (mainIndex) => (event, newValue) => {
    setSelectedSubTab((prev) => ({
      ...prev,
      [mainIndex]: newValue,
    }));
  };

  const focusInput = () => {
    inputRef.current?.focus();
  };
  const onSubmit = (data) => {
    console.log('Form Data Submitted:', data);
    // Perform API call or further processing
    navigate('/user-management/roles'); 
  };

  const handleCancel = () => {
    reset(); // Resets the form fields
    console.log('Form Reset');
  };

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 0.2 }}>
      <IconButton
        sx={{ backgroundColor: '#FFFFFF', width: 60, height: 30, borderRadius: '0px' }}
        onClick={handleSubmit(onSubmit)}
        component={Link}
        to="/user-management/roles"
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'rgba(58, 45, 204, 0.7)',
          }}
        >
          Save
        </Typography>
      </IconButton>
      <IconButton
        sx={{ backgroundColor: '#FFFFFF', width: 60, height: 30, borderRadius: '0px' }}
        onClick={handleCancel}
        component={Link}
        to="/user-management/roles"
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'rgba(26, 24, 24, 0.7)',
          }}
        >
          Cancel
        </Typography>
      </IconButton>
    </Box>
  );
  const formatName = (name) => name.replace(/_/g, " ");
  return (
    <>
      <CustomCardHeader title="Add Roles" secondary={secondaryActions1} />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <MainCard sx={{borderRadius:'0px'}}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid container spacing={3}>
                {/* Username Field */}
                <Grid item xs={12} sm={6} xl={4}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="username">Role Name</InputLabel>
                    <Controller
                      name="username"
                      control={control}
                      rules={{ required: 'Username is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          placeholder="Role Name"
                          autoFocus
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: '#f5f5f5',
                              borderRadius: '2px',
                            },
                          }}
                          error={!!errors.username}
                          helperText={errors.username?.message}
                        />
                      )}
                    />
                  </Stack>
                </Grid>

                {/* Account Email Field */}
                <Grid item xs={12} sm={6} xl={4}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="email">Description</InputLabel>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        required: 'Email is required',
                        pattern: {
                          value: /^[^@]+@[^@]+\.[^@]+$/,
                          message: 'Invalid email format',
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          placeholder="Description"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: '#f5f5f5',
                              borderRadius: '2px',
                            },
                          }}
                          error={!!errors.email}
                          helperText={errors.email?.message}
                        />
                      )}
                    />
                  </Stack>
                </Grid>

                {/* Language Field */}
                <Grid item xs={12} sm={6} xl={4}>
                  <Stack spacing={1}>
                    <InputLabel htmlFor="language">Select Role</InputLabel>
                    <Controller
                      name="language"
                      control={control}
                      rules={{ required: 'Language is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          placeholder="Select Role"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: '#f5f5f5',
                              borderRadius: '2px',
                            },
                          }}
                          error={!!errors.language}
                          helperText={errors.language?.message}
                        />
                      )}
                    />
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </MainCard>
        </Grid>

        <Grid item xs={12} xl ={2}>
          <Grid Container spacing={3}>
          <MainCard sx={{borderRadius:'0px'}}>
        <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}>
          <ListItemButton  >
            <ListItemText primary="Left Menu" />
          </ListItemButton>

        </List>
        <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}>
          <ListItemButton  >
            <ListItemText primary="Top Menu" />
          </ListItemButton>

        </List>
        <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}>
          <ListItemButton  >
            <ListItemText primary="Top Menu" />
          </ListItemButton>

        </List>
        </MainCard>
        </Grid>

        </Grid>

        <Grid item xs={12} xl ={10}>
          {/* <Grid Container spacing={3}> */}
          {/* <MainCard sx={{borderRadius:'0px'}}> */}
          <TableContainer component={Paper} sx={{borderRadius:'0px'}}>
        <Table size="small">
        <TableHead>
          <TableRow sx={{ height: "16px", backgroundColor:"white" }}> {/* Reduced row height */}
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Actions</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Member</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Manager</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Admin</TableCell>
          </TableRow>
        </TableHead>

          <TableBody>
            {permissions.map((permission, categoryIndex) => (
              <React.Fragment key={categoryIndex}>
                {/* Category Header */}
                <TableRow sx={{ height: "24px" }}> {/* Reduced row height */}
                  <TableCell
                    colSpan={4}
                    sx={{
                      padding: "2px", // Reduced padding
                      backgroundColor: "#f5f5f5",
                      fontWeight: "bold",
                      lineHeight: "1",
                      textAlign: "left",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontSize: "0.875rem",
                        display: "flex",
                        alignItems: "center",
                        paddingLeft: "6px", // Reduced padding
                      }}
                    >
                      <span style={{ marginRight: "4px", marginTop:"2px" }}> {/* Reduced margins */}
                        {permission.category === "Jobs management" ? (
                          <WorkIcon fontSize="small" />
                        ) : (
                          <GroupIcon fontSize="small" />
                        )}
                      </span>
                      {permission.category}
                    </Typography>
                  </TableCell>
                </TableRow>
                {/* Actions */}
                {permission.actions.map((action, actionIndex) => (
                  <TableRow key={actionIndex} sx={{ height: "24px" }}> {/* Reduced row height */}
                  <TableCell sx={{ padding: "4px", fontSize: "0.875rem", border: 0 }}>{action.name}</TableCell> {/* Reduced padding */}
                  {["member", "manager", "admin"].map((role) => (
                    <TableCell align="center" key={role} sx={{ padding: "4px", border: 0 }}> {/* Reduced padding */}
                      <CustomCheckbox
                          checked={checked}
                          onChange={() => togglePermission(categoryIndex, actionIndex, role)}
                        />
                        
                      

                    </TableCell>
                  ))}
                </TableRow>
                
                ))}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
        {/* </MainCard> */}
        {/* </Grid> */}

        </Grid>

        <Grid item xs={12}>
        <TableContainer component={Paper} sx={{borderRadius:'0px'}}>
        <Table size="small">
        <TableHead>
          <TableRow sx={{ height: "16px", backgroundColor:"white" }}> {/* Reduced row height */}
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Actions</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Member</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Manager</TableCell>
            <TableCell align="center" sx={{ padding: "2px", fontSize: "0.875rem", fontWeight: "bold" }}>Admin</TableCell>
          </TableRow>
        </TableHead>

          <TableBody>
            {leftMenuData.map((section,sectionIndex ) => (
              <React.Fragment key={sectionIndex}>
                {/* Category Header */}
                <TableRow sx={{ height: "24px" }}> {/* Reduced row height */}
                  <TableCell
                    colSpan={4}
                    sx={{
                      padding: "2px", // Reduced padding
                      backgroundColor: "#f5f5f5",
                      fontWeight: "bold",
                      lineHeight: "1",
                      textAlign: "left",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        fontSize: "0.875rem",
                        display: "flex",
                        alignItems: "center",
                        paddingLeft: "6px", // Reduced padding
                      }}
                    >
                      <span style={{ marginRight: "4px", marginTop:"2px" }}> {/* Reduced margins */}
                        {permission.category === "Jobs management" ? (
                          <WorkIcon fontSize="small" />
                        ) : (
                          <GroupIcon fontSize="small" />
                        )}
                      </span>
                      {section.name}
                    </Typography>
                  </TableCell>
                </TableRow>
                {/* Actions */}
                {section.children?.map((action, actionIndex) => (
                  <TableRow key={actionIndex} sx={{ height: "24px" }}> {/* Reduced row height */}
                  <TableCell sx={{ padding: "4px", fontSize: "0.875rem", border: 0 }}>{action.name}</TableCell> {/* Reduced padding */}
                  {Object.keys(permissionMapping).map((permissionKey) => (
                    <TableCell align="center" key={permissionKey} sx={{ padding: "4px", border: 0 }}> {/* Reduced padding */}
                      <CustomCheckbox
                           checked={isPermissionEnabled(action.permissions, permissionKey)}
                           onChange={() =>
                            console.log(`Toggle ${permissionKey} for ${action.name}`)
                          }
                        />
                        
                      

                    </TableCell>
                  ))}
                </TableRow>
                
                ))}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
        {/* </MainCard> */}
        {/* </Grid> */}

        </Grid>



            <Grid item xs={12}>
            <TableContainer component={Paper} sx={{ borderRadius: "0px", margin: "10px" }}> {/* Reduced margin */}
      <Table size="small"> {/* Added size="small" */}
        {/* Table Head */}
        <TableHead>
          <TableRow sx={{ height: "16px" }}> {/* Reduced row height */}
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Actions</TableCell>
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Read</TableCell>
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Write</TableCell>
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Update</TableCell>
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Delete</TableCell>
            <TableCell sx={{ fontWeight: "bold", textAlign: "center", padding: "2px" }}>Select All</TableCell>
          </TableRow>
        </TableHead>

        {/* Table Body */}
        <TableBody>
          {leftMenuData?.map((section, sectionIndex) => (
            <React.Fragment key={sectionIndex}>
              {/* Category Header Row */}
              <TableRow sx={{ backgroundColor: "#e0e0e0", height: "24px" }}> {/* Reduced row height */}
                <TableCell colSpan={6} sx={{ fontWeight: "bold", fontSize: "0.875rem", padding: "2px" }}>
                  {section.name}
                </TableCell>
              </TableRow>

              {/* Action Rows under each Category */}
              {section.children?.map((action, actionIndex) => (
                <TableRow key={actionIndex} sx={{ height: "24px" }}> {/* Reduced row height */}
                  <TableCell sx={{ padding: "4px" }}>{action.name}</TableCell>
                  {Object.keys(permissionMapping).map((permissionKey) => (
                    <TableCell key={permissionKey} align="center" sx={{ padding: "4px" }}>
                      <Checkbox
                        checked={isPermissionEnabled(action.permissions, permissionKey)}
                        onChange={() =>
                          console.log(`Toggle ${permissionKey} for ${action.name}`)
                        }
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
    </Grid>


        <Grid item xs={12}>
          <MainCard>
            <TabContext value={selectedMainTab}>
                  <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                    <TabList
                      variant="scrollable"
                      scrollButtons="auto"
                      aria-labelledby="tablist-label"

                      onChange={handleMainTabChange}
                      // aria-label="Main tabs"
                    >
                      {data?.map((item, index) => (
                        <Tab
                          sx={{ textTransform: "none" }} // Custom styles for each Tab
                          label={formatName(item.name)} // Display the name in the Tab
                          value={index.toString()} // Sets the value (stringified index)
                          key={index} // Unique key for React's reconciliation
                        />
                      ))}
 
                    </TabList>
                  </Box>
                  {data?.map((item, mainIndex) => (
              <TabPanel value={mainIndex.toString()} key={mainIndex}>
                {/* Check if there are children to display nested tabs */}
                {item.children && item.children.length > 0 && (
                  <TabContext value={selectedSubTab[mainIndex] || "0"}>
                    <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                      <TabList
                        variant="scrollable"
                        scrollButtons="auto"
                        onChange={handleSubTabChange(mainIndex)}
                        aria-label="Sub tabs"
                      >
                        {item.children.map((child, childIndex) => (
                          <Tab
                            sx={{ textTransform: "none" }}
                            label={formatName(child.name)}
                            value={childIndex.toString()}
                            key={childIndex}
                          />
                        ))}
                      </TabList>
                    </Box>
                    <Box
                      sx={{
                        height: `calc(100vh - 360px)`,
                        overflowY: "auto",
                      }}
                    >
                      {item.children.map((child, childIndex) => (
                        <TabPanel
                          value={childIndex.toString()}
                          key={childIndex}
                        >
                          <PermissionItem
                            key={child.name}
                            item={child}
                            updatePermissions={null}
                          />
                        </TabPanel>
                      ))}
                    </Box>
                  </TabContext>
                )}
              </TabPanel>
            ))}
                </TabContext>
               
          </MainCard>
        </Grid>     
    </Grid>
    </>
  );
};


<Grid item xs={12}>
<TableContainer component={Paper} sx={{ borderRadius: "0px", margin: "20px" }}>
<Table>
{/* Table Head */}
<TableHead>
<TableRow>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Actions</TableCell>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Read</TableCell>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Write</TableCell>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Update</TableCell>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Delete</TableCell>
<TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>Select All</TableCell>
</TableRow>
</TableHead>

{/* Table Body */}
<TableBody>
{leftMenuData?.map((section, sectionIndex) => (
<React.Fragment key={sectionIndex}>
  {/* Category Header Row (e.g., "Client", "Employee") */}
  <TableRow sx={{ backgroundColor: "#e0e0e0" }}>
    <TableCell colSpan={6} sx={{ fontWeight: "bold", fontSize: "1rem" }}>
      {section.name}
    </TableCell>
  </TableRow>

  {/* Action Rows under each Category */}
  {section.children?.map((action, actionIndex) => (
    <TableRow key={actionIndex}>
      <TableCell>{action.name}</TableCell>
      {Object.keys(permissionMapping).map((permissionKey) => (
        <TableCell key={permissionKey} align="center">
          <Checkbox
            checked={isPermissionEnabled(action.permissions, permissionKey)}
            onChange={() =>
              console.log(`Toggle ${permissionKey} for ${action.name}`)
            }
          />
        </TableCell>
      ))}
    </TableRow>
  ))}
</React.Fragment>
))}
</TableBody>
</Table>
</TableContainer>
</Grid>


<Grid item xs={12}>
<MainCard>
<TabContext value={selectedMainTab}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <TabList
          variant="scrollable"
          scrollButtons="auto"
          aria-labelledby="tablist-label"

          onChange={handleMainTabChange}
          // aria-label="Main tabs"
        >
          {data?.map((item, index) => (
            <Tab
              sx={{ textTransform: "none" }} // Custom styles for each Tab
              label={formatName(item.name)} // Display the name in the Tab
              value={index.toString()} // Sets the value (stringified index)
              key={index} // Unique key for React's reconciliation
            />
          ))}

        </TabList>
      </Box>
      {data?.map((item, mainIndex) => (
  <TabPanel value={mainIndex.toString()} key={mainIndex}>
    {/* Check if there are children to display nested tabs */}
    {item.children && item.children.length > 0 && (
      <TabContext value={selectedSubTab[mainIndex] || "0"}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <TabList
            variant="scrollable"
            scrollButtons="auto"
            onChange={handleSubTabChange(mainIndex)}
            aria-label="Sub tabs"
          >
            {item.children.map((child, childIndex) => (
              <Tab
                sx={{ textTransform: "none" }}
                label={formatName(child.name)}
                value={childIndex.toString()}
                key={childIndex}
              />
            ))}
          </TabList>
        </Box>
        <Box
          sx={{
            height: `calc(100vh - 360px)`,
            overflowY: "auto",
          }}
        >
          {item.children.map((child, childIndex) => (
            <TabPanel
              value={childIndex.toString()}
              key={childIndex}
            >
              <PermissionItem
                key={child.name}
                item={child}
                updatePermissions={null}
              />
            </TabPanel>
          ))}
        </Box>
      </TabContext>
    )}
  </TabPanel>
))}
    </TabContext>
   
</MainCard>
</Grid>     

export default AddRoles;
