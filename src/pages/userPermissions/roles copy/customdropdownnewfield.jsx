import React, { useEffect, useState } from "react";
import { FormControl, MenuItem, Select, InputLabel, FormHelperText } from "@mui/material";
import { Controller } from "react-hook-form";

const CustomDropdownField = ({
  name,
  control,
  placeholder,
  getOptions = () => [], // Default function to avoid undefined
  sx = {},
  defaultValue = "",
  error,
  helperText,
  ...props
}) => {
  const [dropdownOptions, setDropdownOptions] = useState([]);

  useEffect(() => {
    const options = getOptions();
    setDropdownOptions(options);
  }, [getOptions]);

  return (
    <FormControl fullWidth error={error}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            size="small"
            fullWidth
            value={field.value || ""}
            onChange={(e) => {
              field.onChange(e.target.value);
              if (props.onChange) {
                props.onChange(e.target.value);
              }
            }}
            displayEmpty
            variant="outlined"
            renderValue={(selected) => (selected ? selected : <em>{placeholder}</em>)}
            sx={{
              backgroundColor: sx.backgroundColor || "white",
              borderRadius: "2px",
              "& .MuiSelect-select": {
                backgroundColor: sx.backgroundColor || "white",
              },
              "& fieldset": {
                borderRadius: "0px",
              },
              ...sx,
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  borderRadius: "0px !important",
                  padding: "0 !important",
                  margin: "0 !important",
                },
              },
              MenuListProps: {
                sx: {
                  paddingTop: "0 !important",
                  paddingBottom: "0 !important",
                },
              },
            }}
            {...props}
          >
            {dropdownOptions.map((option) => (
              <MenuItem
                key={option.id}
                value={option.id}
                sx={{
                  border: "1px solid transparent",
                  borderRadius: "0px",
                  padding: "4px",
                  "&:hover": {
                    borderColor: "black",
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                  "&.Mui-focusVisible": {
                    borderColor: "black",
                  },
                }}
              >
                {option.role_name}
              </MenuItem>
            ))}
          </Select>
        )}
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default CustomDropdownField;

// Example of how to use the component
const getRoles = () => [
  {
    id: "6e8cc34a-e9f4-11ef-bc93-6c24086040f8",
    role_name: "Super Admin",
    description: "Has all permissions",
    parent_role_id: "bc64428b-e9f3-11ef-bc93-6c24086040f8",
    level: "Level 1",
  },
  {
    id: "b5966ead-e9f4-11ef-bc93-6c24086040f8",
    role_name: "Admin",
    description: "Has Limited permissions",
    parent_role_id: "6e8cc34a-e9f4-11ef-bc93-6c24086040f8",
    level: "Level 2",
  },
];

// <CustomDropdownField name="role" control={control} getOptions={getRoles} placeholder="Select Role" onChange={(val) => console.log(val)} />
