import React from "react";
import { Checkbox } from "@mui/material";

// Custom Icons
const CustomUncheckedIcon = () => (
  <div
    style={{
      width: "18px",
      height: "18px",
    //   border: "2px solid black", // Black border
      borderRadius: "0px", // Rounded corners
      backgroundColor: "transparent", // Transparent background
    }}
  />
);

const CustomCheckedIcon = () => (
  <div
    style={{
      width: "18px",
      height: "18px",
      border: "rgb(91, 88, 88)", // Black border
      
      borderRadius: "0px", // Rounded corners
      backgroundColor: "#f5f5f5",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
    }}
  >
    {/* Check Icon */}
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 24 24"
      fill="none"
      stroke="black" // Black tick mark
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 6L9 17l-5-5" />
    </svg>
  </div>
);

const CustomCheckbox = ({ checked, onChange }) => (
  <Checkbox
    checked={checked}
    onChange={onChange}
    icon={<CustomUncheckedIcon />} // Unchecked icon
    checkedIcon={<CustomCheckedIcon />} // Checked icon
    sx={{
      padding: 0, // Remove extra padding
    }}
  />
);

export default CustomCheckbox;
