// import React, { useState, useMemo,useEffect, } from 'react';
// import Chip from '@mui/material/Chip';
// import LinearWithLabel from 'components/@extended/progress/LinearWithLabel'; // Custom Progress Component
// import SettingsIcon from '@mui/icons-material/Settings';
// import ThumbUpIcon from '@mui/icons-material/ThumbUp';
// import DownloadIcon from '@mui/icons-material/Download';
// import FileCopyIcon from '@mui/icons-material/FileCopy';
// import GroupIcon from '@mui/icons-material/Group';
// import IconButton from '@mui/material/IconButton';
// import { Typography } from '@mui/material';
// import { Box } from '@mui/system';
// import {
//   getCoreRowModel,
//   getFilteredRowModel,
//   getFacetedRowModel,
//   getFacetedMinMaxValues,
//   getFacetedUniqueValues,
//   getPaginationRowModel,
//   getSortedRowModel,
//   getGroupedRowModel,
//   getExpandedRowModel,
//   flexRender,
//   useReactTable,
//   sortingFns
// } from '@tanstack/react-table';
// import { alpha, useTheme } from '@mui/material/styles';
// import { DebouncedInput } from 'components/third-party/react-table';
// import { Link } from 'react-router-dom';
// import { useNavigate } from "react-router-dom";
// import CustomCardHeader from 'components/custom-components/CustomCardHeader';
// import CustomTableContainer from 'components/custom-components/CustomTableContainer';
// import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
// import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
// import TablePagination from 'components/custom-components/CustomPaginationItems';
// import data from './staticdata';
// import ModeEditIcon from '@mui/icons-material/ModeEdit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import axios from 'axios';
// import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
// import useMediaQuery from '@mui/material/useMediaQuery';

// import { useRBAC } from 'pages/permissions/RBACContext';
// import { PERMISSIONS } from 'constants';

// import Tooltip from '@mui/material/Tooltip';
// import ConfirmDeleteDialog from "pages/jobrequest/jorequestdeletedialog";

// export const fuzzyFilter = (row, columnId, value, addMeta) => {
//   // rank the item
//   const itemRank = rankItem(row.getValue(columnId), value);

//   // store the ranking info
//   addMeta(itemRank);

//   // return if the item should be filtered in/out
//   return itemRank.passed;
// };

// export const fuzzySort = (rowA, rowB, columnId) => {
//   let dir = 0;

//   // only sort by rank if the column has ranking information
//   if (rowA.columnFiltersMeta[columnId]) {
//     dir = compareItems(rowA.columnFiltersMeta[columnId], rowB.columnFiltersMeta[columnId]);
//   }

//   // provide an alphanumeric fallback for when the item ranks are equal
//   return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
// };

// const ParentComponent = () => {

//    const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
//     const [isActive, setIsActive] = useState(true);
//    const theme = useTheme();
//    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
//    console.log("Mobile View",isMobile)

//    const [roleId, setRolesId]  = useState(null);
//   console.log("this is role id", roleId)

//    const handleDeleteClick = (rowData) => {
//     console.log("Confirming Roles delete with isActive:", isActive);
//     setRolesId(rowData.id);
//     console.log("this is job request", rowData)
//     setIsActive(rowData.is_active);
//     console.log("this is job request active",rowData.is_active)
//     setConfirmDeleteOpen(true);
//   };

//   const { canMenuPage } = useRBAC();

//   const canAddRole = canMenuPage("Top_Menu", "Roles", PERMISSIONS.WRITE);
//   const canEditRole = canMenuPage("Top_Menu", "Roles", PERMISSIONS.EXECUTE);
//   const canDeleteRole = canMenuPage("Top_Menu", "Roles", PERMISSIONS.DELETE);

//   console.log("Add Role",canAddRole)
//   console.log("Edit Role",canEditRole)
//   console.log("Delete Role",canDeleteRole)

//   // useEffect(() => {
//   //   const fetchRoles = async () => {
//   //     try {
//   //       const token = localStorage.getItem('serviceToken');
//   //       const response = await axios.get("http://localhost:8080/roles",  {
//   //         method: 'GET',
//   //         headers: {
//   //           'Authorization': `Bearer ${token}`,
//   //           'Content-Type': 'application/json',
//   //         },
//   //       });

//   //       setData(response.data)

//   //     } catch (error) {
//   //       console.error("Error fetching roles:", error);
//   //     }
//   //   };

//   //   fetchRoles();
//   // }, []);
//   const fetchRoles = async () => {
//   try {
//     const token = localStorage.getItem('serviceToken');
//     const response = await axios.get("http://localhost:8080/roles",  {
//       method: 'GET',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'Content-Type': 'application/json',
//       },
//     });

//     setData(response.data);  // Set the updated roles data
//   } catch (error) {
//     console.error("Error fetching roles:", error);
//   }
// };

//   useEffect(() => {
//     fetchRoles();
//     }, []);

//    const handleConfirmDelete = async () => {
//     try {
//       // Prepare the payload for the PUT request
//       const payload = {
//         is_active: !isActive, // Toggle the is_active status
//       };

//       // Log the payload to the console before sending the request
//       console.log('Payload being sent:', payload);

//       const response = await fetch(`http://localhost:8080/roles/delete-role/${roleId}`, {
//         method: 'PUT',
//         headers: {
//           'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(payload), // Send the payload as a JSON string
//       });

//       // Log the response status and body
//       console.log('Response status:', response.status);
//       const responseBody = await response.json(); // Read the response body
//       console.log('Response body:', responseBody); // Log the response body
//       fetchRoles();

//     } catch (error) {
//       console.error('Error updating job request status:', error);
//     } finally {
//       setConfirmDeleteOpen(false);
//     }
//   };

//   const columns = useMemo(
//   () => [
//     {
//       header: "Name",
//       accessorKey: "role_name",
//       cell: ({ getValue }) => {
//         const roleName = getValue();
//         return (
//           <Tooltip title={roleName} placement="bottom">
//             <Typography variant="body2" sx={{ display: "inline-block" }}>
//               {roleName}
//             </Typography>
//           </Tooltip>
//         );
//       },
//     },
//     {
//       header: "Level",
//       accessorKey: "level_name",
//       cell: ({ getValue }) => {
//         const levelName = getValue();
//         return (
//           <Tooltip title={levelName} placement="bottom">
//             <Typography variant="body2" sx={{ display: "inline-block" }}>
//               {levelName}
//             </Typography>
//           </Tooltip>
//         );
//       },
//     },
//     {
//       header: "Description",
//       accessorKey: "description",
//       cell: ({ getValue }) => {
//         const description = getValue();
//         return (
//           <Tooltip title={description} placement="bottom">
//             <Typography variant="body2" sx={{ display: "inline-block" }}>
//               {description}
//             </Typography>
//           </Tooltip>
//         );
//       },
//     },
//     {
//       header: "Parent Role",
//       accessorKey: "ParentRole.role_name",
//       cell: ({ getValue }) => {
//         const parentRole = getValue();
//         return (
//           <Tooltip title={parentRole} placement="bottom">
//             <Typography variant="body2" sx={{ display: "inline-block" }}>
//               {parentRole}
//             </Typography>
//           </Tooltip>
//         );
//       },
//     },
//     {
//       header: "Status",
//       accessorKey: "is_active",
//       cell: (props) => {
//         const isActive = props.getValue(); // Get the exact value

//         console.log("Status Value for Row:", isActive); // ✅ Debugging log

//         if (isActive === true) {
//           return <Chip color="success" label="Active" size="small" variant="light" />;
//         } else if (isActive === false) {
//           return <Chip color="error" label="Inactive" size="small" variant="light" />;
//         } else {
//           return <Chip color="warning" label="Unknown" size="small" variant="light" />;
//         }
//       },
//     },

//     {
//       header: "Actions",
//       accessorKey: "actions",
//       cell: ({ row }) => (
//         <Box>
//           {canEditRole && (
//             <Tooltip title="Edit" placement="bottom">
//               <IconButton
//                 sx={{
//                   padding: 0.2, // Reduce padding
//                   height: 24, // Reduce height
//                   width: 24, // Reduce width to make it smaller
//                   minWidth: "unset", // Remove minimum width restriction
//                 }}
//                 onClick={() =>
//                   navigate("/user-management/edit-roles", {
//                     state: { row: JSON.parse(JSON.stringify(row)) },
//                   })
//                 }
//               >
//                 <ModeEditIcon />
//               </IconButton>
//             </Tooltip>
//           )}

//           {canDeleteRole && (
//             <Tooltip title="Delete" placement="bottom">
//               <IconButton
//                 sx={{
//                   padding: 0.2, // Reduce padding
//                   height: 24, // Reduce height
//                   width: 24, // Reduce width to make it smaller
//                   minWidth: "unset", // Remove minimum width restriction
//                 }}
//                 onClick={() => handleDeleteClick(row.original)}
//                 style={{
//                   color: row.original.is_active ? "red" : "gray", // Highlight delete in red if active
//                 }}
//               >
//                 <DeleteIcon />
//               </IconButton>
//             </Tooltip>
//           )}
//         </Box>
//       ),
//     },
//   ],
//   [canEditRole, canDeleteRole] // Make sure to re-render if permissions change
// );

// // useEffect(() => {
// //   fetchUserRolesRequests();
// // }, []);

// const navigate = useNavigate();

// const  [data, setData] = useState([]);
// console.log("this is data ************** ", data);
// const [rowSelection, setRowSelection] = useState({});
// const [columnFilters, setColumnFilters] = useState([]);
// const [globalFilter, setGlobalFilter] = useState('');

// const [sorting, setSorting] = useState([]);
// const [columnVisibility, setColumnVisibility] = useState({});
// const [originalData, setOriginalData] = useState(() => [...data]);

// useEffect(() => {

//   const fetchRoles = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.get("http://localhost:8080/roles",  {
//         method: 'GET',
//         headers: {
//           'Authorization': `Bearer ${token}`,
//           'Content-Type': 'application/json',
//         },
//       });

//       setData(response.data)

//     } catch (error) {
//       console.error("Error fetching roles:", error);
//     }
//   };

// const table = useReactTable({
//   data,
//   columns,
//   state: {
//     rowSelection,
//     columnFilters,
//     globalFilter,
//     sorting,
//     columnVisibility,
//   },

//   onColumnFiltersChange: setColumnFilters,

//   onSortingChange: setSorting,
//   getCoreRowModel: getCoreRowModel(),
//   getFilteredRowModel: getFilteredRowModel(),
//   getPaginationRowModel: getPaginationRowModel(),
//   getSortedRowModel: getSortedRowModel(),
//   onColumnVisibilityChange: setColumnVisibility,
//   globalFilterFn: fuzzyFilter,
//   onGlobalFilterChange: (value) => {
//     console.log('🔍 onGlobalFilterChange triggered:', value); // Debugging log
//     setGlobalFilter(value);
//   },
//    onRowSelectionChange: setRowSelection,
//   meta: {
//     updateData: (rowIndex, columnId, value) => {
//       setData((old) =>
//         old.map((row, index) => {
//           if (index === rowIndex) {
//             return {
//               ...old[rowIndex],
//               [columnId]: value,
//             };
//           }
//           return row;
//         })
//       );
//     },
//     revertData: (rowIndex, revert) => {
//       if (revert) {
//         setData((old) =>
//           old.map((row, index) => (index === rowIndex ? originalData[rowIndex] : row))
//         );
//       } else {
//         setOriginalData((old) =>
//           old.map((row, index) => (index === rowIndex ? data[rowIndex] : row))
//         );
//       }
//     },
//   },
// });

// let headers = [];
// table.getVisibleLeafColumns().map(
//   (columns) =>
//     // @ts-ignore
//     columns.columnDef.accessorKey &&
//     headers.push({
//       label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
//       // @ts-ignore
//       key: columns.columnDef.accessorKey
//     })
// );
// useEffect(() => setColumnVisibility({ id: false, status :true}), []);

// const secondaryActions2 = (
//   <Box sx={{ display: "flex", gap: 1 }}>
//     <IconButton
//       sx={{
//         backgroundColor: "primary.main",
//         width: 40,
//         height: 30,
//         borderRadius: "6px",
//         border: "1px solid grey",
//         "&:hover": { backgroundColor: "primary.main" },
//         "&:focus": { backgroundColor: "primary.main" },
//         color: "white",
//       }}
//     >
//       <DownloadIcon />
//     </IconButton>

//     <IconButton
//      onClick={() => navigate("/user-management/add-roles")}
//       sx={{
//         backgroundColor: "primary.main",
//         width: 60,
//         height: 30,
//         borderRadius: "6px",
//         border: "1px solid grey",
//         "&:hover": { backgroundColor: "primary.main" },
//         "&:focus": { backgroundColor: "primary.main" },
//       }}
//     >
//       <Typography
//         sx={{
//           fontFamily: "Arial, sans-serif",
//           fontSize: "14px",
//           color: "white",
//         }}
//       >
//         + Add
//       </Typography>
//     </IconButton>
//   </Box>
// );

// const secondaryActions1 = (

// <Box sx={{ display: "flex", gap: 1 }}>
//   <DebouncedInput
//       value={globalFilter ?? ''}
//       onFilterChange={(value) => setGlobalFilter(String(value))}
//       placeholder={`Search records...`}
//     />

// </Box>
// );

// const secondaryActions3 = (
// <Box sx={{ display: "flex", gap: 1 }}>
//   <DebouncedInput
//       value={globalFilter ?? ''}
//       onFilterChange={(value) => setGlobalFilter(String(value))}
//       placeholder={`Search records...`}
//     />
//   <IconButton
//     sx={{
//       backgroundColor: "primary.main",
//       width: 40,
//       height: 30,
//       borderRadius: "6px",
//       border: "1px solid grey",
//       "&:hover": { backgroundColor: "primary.main" },
//       "&:focus": { backgroundColor: "primary.main" },
//       color: "white",
//     }}
//   >
//     <DownloadIcon />
//   </IconButton>
//   {canAddRole && (
//   <IconButton
//     onClick={() => navigate("/user-management/add-roles")}
//     sx={{
//       backgroundColor: "primary.main",
//       width: 60,
//       height: 30,
//       borderRadius: "6px",
//       border: "1px solid grey",
//       "&:hover": { backgroundColor: "primary.main" },
//       "&:focus": { backgroundColor: "primary.main" },
//     }}
//   >
//     <Typography
//       sx={{
//         fontFamily: "Arial, sans-serif",
//         fontSize: "14px",
//         color: "white",
//       }}
//     >
//       + Add
//     </Typography>
//   </IconButton>
//   )}
// </Box>
// );

//   const paginationbutton = (
//     <TablePagination
//     {...{
//       setPageSize: table.setPageSize,
//       setPageIndex: table.setPageIndex,
//       getState: table.getState,
//       getPageCount: table.getPageCount,
//     }}
//   />);

//   const rowbutton =( <Box sx={{ display: 'flex', gap: 2 }}>
//     <RowsPerPageSelector  {...{

//     getState: table.getState,
//     setPageSize: table.setPageSize,

//   }}/>
//   <TablePagination
//     {...{
//       setPageSize: table.setPageSize,
//       setPageIndex: table.setPageIndex,
//       getState: table.getState,
//       getPageCount: table.getPageCount,
//     }}
//   />
//   </Box>)

//   return (
//     <>

//      <ConfirmDeleteDialog
//                 open={confirmDeleteOpen}
//                 handleClose={() => setConfirmDeleteOpen(false)}
//                 handleConfirm={handleConfirmDelete}
//                 isActive={isActive}
//               />
//     {isMobile ? (
//                   <>
//                     {/* 🔹 Mobile View: Title + Actions in One Line */}
//                     <CustomCardHeader
//                       title={<Typography variant="h6" sx={{ fontSize: 25 }}>Roles</Typography>}
//                       secondary={secondaryActions2}
//                     />
//                     <CustomCardHeader secondary={secondaryActions1} />
//                     <CustomCardHeader secondary={<ColumnVisibilitySelector table={table} sx={{ padding: "5px" }} />}  />

//                   </>
//                 ) : (
//                   <>
//                     {/* 🔹 Desktop View: Actions (Right), Search (Left) */}
//                     <CustomCardHeader
//                       title={<Typography variant="h6" sx={{ fontSize: 25 }}>Roles</Typography>}

//                     />
//                     <CustomCardHeader title={<ColumnVisibilitySelector table={table} sx={{ padding: "5px" }}  />}  secondary={secondaryActions3}   />

//                   </>
//                 )}

//                 <CustomTableContainer table={table} rowSelection={rowSelection} theme={theme} />
//                 <CustomCardHeader secondary={rowbutton} />
//               </>
//             );

//           };
// export default ParentComponent;

import React, { useState, useMemo, useEffect } from 'react';
import Chip from '@mui/material/Chip';
import LinearWithLabel from 'components/@extended/progress/LinearWithLabel'; // Custom Progress Component
import SettingsIcon from '@mui/icons-material/Settings';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import DownloadIcon from '@mui/icons-material/Download';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import GroupIcon from '@mui/icons-material/Group';
import IconButton from '@mui/material/IconButton';
import { CircularProgress, Menu, MenuItem, Typography } from '@mui/material';
import { Box } from '@mui/system';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  sortingFns
} from '@tanstack/react-table';
import { alpha, useTheme } from '@mui/material/styles';
import { CSVExport, DebouncedInput } from 'components/third-party/react-table';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import CustomTableContainer from 'components/custom-components/CustomTableContainer';
import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import data from './staticdata';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import axios from 'axios';
import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
import useMediaQuery from '@mui/material/useMediaQuery';

import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS } from 'constants';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import Tooltip from '@mui/material/Tooltip';
import ConfirmDeleteDialog from 'pages/jobrequest/jorequestdeletedialog';
import Spinner from 'components/custom-components/Spinner';

const API_URL = import.meta.env.VITE_APP_API_URL;

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

export const fuzzySort = (rowA, rowB, columnId) => {
  let dir = 0;
  if (rowA.columnFiltersMeta[columnId]) {
    dir = compareItems(rowA.columnFiltersMeta[columnId], rowB.columnFiltersMeta[columnId]);
  }
  return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
};

const ParentComponent = () => {
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [roleId, setRolesId] = useState(null);

  const handleDeleteClick = (rowData) => {
    setRolesId(rowData.id);
    setIsActive(rowData.is_active);
    setConfirmDeleteOpen(true);
  };

  const { canMenuPage } = useRBAC();
  const canAddRole = canMenuPage('Top_Menu', 'Roles', PERMISSIONS.WRITE);
  const canEditRole = canMenuPage('Top_Menu', 'Roles', PERMISSIONS.EXECUTE);
  const canDeleteRole = canMenuPage('Top_Menu', 'Roles', PERMISSIONS.DELETE);
  const [loading, setLoading] = useState(true);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await axios.get(`${API_URL}/roles`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      setTimeout(() => {
        setData(response.data);
        setLoading(false); // Stop loading
      });
    } catch (error) {
      console.error('Error fetching roles:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  const handleConfirmDelete = async () => {
    try {
      const payload = {
        is_active: !isActive
      };
      const response = await fetch(`${API_URL}/roles/delete-role/${roleId}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('serviceToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      fetchRoles();
    } catch (error) {
      console.error('Error updating role status:', error);
    } finally {
      setConfirmDeleteOpen(false);
    }
  };

  const columns = useMemo(
    () => [
      {
        header: 'Name',
        accessorKey: 'role_name',
        cell: ({ getValue }) => {
          const roleName = getValue();
          return (
            <Tooltip title={roleName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {roleName}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Level',
        accessorKey: 'level_name',
        cell: ({ getValue }) => {
          const levelName = getValue();
          return (
            <Tooltip title={levelName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {levelName}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: ({ getValue }) => {
          const description = getValue();
          return (
            <Tooltip title={description} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {description}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Parent Role',
        accessorKey: 'ParentRole.role_name',
        cell: ({ getValue }) => {
          const parentRole = getValue();
          return (
            <Tooltip title={parentRole} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {parentRole}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Status',
        accessorKey: 'is_active',
        cell: ({ getValue }) => {
          const isActive = getValue();
          const statusLabel = isActive === true ? 'Active' : isActive === false ? 'Inactive' : 'Unknown';
          const chipColor = isActive === true ? 'success' : isActive === false ? 'error' : 'warning';
          return (
            <Tooltip title={statusLabel} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                <Chip color={chipColor} label={statusLabel} size="small" variant="light" />
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: "Actions",
      //   accessorKey: "actions",
      //   cell: ({ row }) => (
      //     <Box>
      //       {canEditRole && (
      //         <Tooltip title="Edit" placement="bottom">
      //           <IconButton
      //             sx={{
      //               padding: 0.2, // Reduce padding
      //               height: 24, // Reduce height
      //               width: 24, // Reduce width to make it smaller
      //               minWidth: "unset", // Remove minimum width restriction
      //             }}
      //             onClick={() =>
      //               navigate("/user-management/edit-roles", {
      //                 state: { row: JSON.parse(JSON.stringify(row)) },
      //               })
      //             }
      //           >
      //             <ModeEditIcon />
      //           </IconButton>
      //         </Tooltip>
      //       )}

      //       {canDeleteRole && (
      //         <Tooltip title="Delete" placement="bottom">
      //           <IconButton
      //             sx={{
      //               padding: 0.2, // Reduce padding
      //               height: 24, // Reduce height
      //               width: 24, // Reduce width to make it smaller
      //               minWidth: "unset", // Remove minimum width restriction
      //             }}
      //             onClick={() => handleDeleteClick(row.original)}
      //             style={{
      //               color: row.original.is_active ? "red" : "gray", // Highlight delete in red if active
      //             }}
      //           >
      //             <DeleteIcon />
      //           </IconButton>
      //         </Tooltip>
      //       )}
      //     </Box>
      //   ),
      // },
      {
        header: 'Actions',
        id: 'actions',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);

          const handleMenuClick = (event) => {
            event.stopPropagation(); // Prevent row selection on click
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          return (
            <>
              <IconButton onClick={handleMenuClick}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
              >
                <MenuItem
                  onClick={() =>
                    navigate('/user-management/edit-roles', {
                      state: { row: JSON.parse(JSON.stringify(row)) }
                    })
                  }
                >
                  Edit
                </MenuItem>
                {/* <MenuItem onClick={() => navigate('/leads/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}>View</MenuItem> */}
                <MenuItem onClick={() => handleDeleteClick(row.original)}>Delete</MenuItem>
              </Menu>
            </>
          );
        }
      }
    ],
    [canEditRole, canDeleteRole]
  );

  const navigate = useNavigate();

  const [data, setData] = useState([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [originalData, setOriginalData] = useState(() => [...data]);

  useEffect(() => {
    fetchRoles();
  }, []);

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onColumnFiltersChange: setColumnFilters,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    globalFilterFn: fuzzyFilter,
    onGlobalFilterChange: (value) => {
      console.log('🔍 onGlobalFilterChange triggered:', value); // Debugging log
      setGlobalFilter(value);
    },
    onRowSelectionChange: setRowSelection,
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      },
      revertData: (rowIndex, revert) => {
        if (revert) {
          setData((old) => old.map((row, index) => (index === rowIndex ? originalData[rowIndex] : row)));
        } else {
          setOriginalData((old) => old.map((row, index) => (index === rowIndex ? data[rowIndex] : row)));
        }
      }
    }
  });

  let headers = [];
  table.getVisibleLeafColumns().map(
    (columns) =>
      // @ts-ignore
      columns.columnDef.accessorKey &&
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        // @ts-ignore
        key: columns.columnDef.accessorKey
      })
  );

  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey) // ✅ Keep only columns with data keys
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean); // ✅ Clean out undefined/empty headers
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000); // ⏱ change every 2 seconds (you can adjust)

    return () => clearInterval(interval); // Cleanup
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const paginationbutton = (
    <TablePagination
      {...{
        setPageSize: table.setPageSize,
        setPageIndex: table.setPageIndex,
        getState: table.getState,
        getPageCount: table.getPageCount
      }}
    />
  );

  const rowbutton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  const secondaryActions2 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      {/* <CSVExport
                        {...{
                          data:
                            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
                              ? data
                              : table.getSelectedRowModel().flatRows.map((row) => row.original),
                          headers,
                          filename: 'Roles.csv'
                        }}
                      /> */}
      {canAddRole && (
        <IconButton
          onClick={() => navigate('/user-management/add-roles')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'primary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
    </Box>
  );
  const secondaryActions3 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />

      <ColumnVisibilitySelector table={table} sx={{ padding: '5px' }} />
      {/* <CSVExport
                          {...{
                            data:
                              table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
                                ? data
                                : table.getSelectedRowModel().flatRows.map((row) => row.original),
                            headers,
                            filename: 'Roles.csv'
                          }}
                        /> */}
      {canAddRole && (
        <IconButton
          onClick={() => navigate('/user-management/add-roles')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'primary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  return (
    <>
      <ConfirmDeleteDialog
        open={confirmDeleteOpen}
        handleClose={() => setConfirmDeleteOpen(false)}
        handleConfirm={handleConfirmDelete}
        isActive={isActive}
        entityType="Roles"
      />
      <CustomTableContainer
        table={table}
        onAddClick={() => navigate('/user-management/add-roles')}
        csvFilename="Roles-list.csv"
        showAddButton={canAddRole}
        addLabel="Roles"
        data={data}
        rowSelection={rowSelection}
        theme={theme}
      />
    </>
  );
};

export default ParentComponent;
