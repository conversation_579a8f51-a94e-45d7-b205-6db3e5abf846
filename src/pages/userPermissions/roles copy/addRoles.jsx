import React, { useState, useEffect, useContext, useMemo } from 'react';
import { useForm, Controller } from 'react-hook-form';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import { borderRadius, Box } from '@mui/system';
import { Tab, Checkbox } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import IconButton from '@mui/material/IconButton';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
// import { useNavigate } from 'react-router-dom';
import { Tab<PERSON>onte<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabPanel } from '@mui/lab';
// project-imports
import MainCard from 'components/MainCard';
import { useRef } from 'react';
import { Outlet } from 'react-router';
import ProfileTab from 'pages/userPermissions/roles/ProfileTab';
import data from './staticdata';
import CustomCheckbox from './checkbox';
import WorkIcon from '@mui/icons-material/Work';
import GroupIcon from '@mui/icons-material/Group';
import MenuIcon from '@mui/icons-material/Menu';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import axios from 'axios';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Select, MenuItem } from '@mui/material';
import List from '@mui/material/List';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';
import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
import CustomNameField from 'components/custom-components/CustomNameField';
// import CustomDropdownField from './customdropdownnewfield';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import Button from '@mui/material/Button';
import { Snackbar, Alert } from '@mui/material';

import JWTContext from 'contexts/JWTContext';
import CardActions from '@mui/material/CardActions';
import { HEADER_HEIGHT } from 'config';
import { useTheme } from '@mui/material/styles';
import Spinner from 'components/custom-components/Spinner';

const API_URL = import.meta.env.VITE_APP_API_URL;
const AddRoles = () => {
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };
  console.log('checking AddRoles');
  const defaultValues = {
    email: '',
    username: '',
    permissions: {}
  };
  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
    watch,
    setValue
  } = useForm({
    defaultValues
  });

  const [updatedPermissions, setUpdatedPermissions] = useState({});
  const [levels, setLevels] = useState([]);
  console.log('this is levels data', levels);
  const [selectedLevel, setSelectedLevel] = useState('');
  useEffect(() => {
    const fetchLevels = async () => {
      const token = localStorage.getItem('serviceToken');

      if (!token) {
        console.error('❌ No token found in localStorage. User may not be logged in.');
        return;
      }

      try {
        const res = await axios.get(`${API_URL}/levelhierarchies/`, {
          headers: {
            Authorization: `Bearer ${token}` // ✅ Attach token to request
          }
        });

        console.log('✅ Response Data:', res.data);
        // console.log("🔍 User Level:", userLevel);

        // ✅ Ensure userAllowedLevels is correctly defined
        // const userAllowedLevels = res.data.find((l) => l.level === userLevel)?.can_create || [];
        // console.log("🚀 User Allowed Levels:", userAllowedLevels);

        // ✅ Ensure levels is correctly filtered
        setLevels(res.data);
      } catch (error) {
        console.error('❌ Error fetching levels:', error);

        if (error.response) {
          console.error('🚨 Response Status:', error.response.status);
          console.error('🚨 Response Data:', error.response.data);

          if (error.response.status === 401) {
            console.error('❌ Unauthorized: Invalid or expired token.');
            alert('Session expired. Please log in again.');
            // Optionally redirect to login page:
            // window.location.href = "/login";
          }
        }
      }
    };

    fetchLevels();
  }, []);

  const permissionMapping = {
    Read: 1, // Binary 0001
    Write: 2, // Binary 0010
    Update: 4, // Binary 0100
    Delete: 8, // Binary 1000
    Select_All: 15 // Full permissions
  };

  const isPermissionEnabled = (permissions, permissionKey) => {
    if (!permissions) return false; // Ensure undefined or 0 means unchecked
    const bitValue = permissionMapping[permissionKey];
    return (permissions & bitValue) === bitValue;
  };

  const [previousPermissions, setPreviousPermissions] = useState({});

  //   const handlePermissionToggle = (action, permissionKey) => {
  //     setUpdatedPermissions((prev) => {
  //       const currentPermissions = prev[action.name] ?? action.permissions;
  //       const bitValue = permissionMapping[permissionKey];

  //       let newPermissions;

  //       if (permissionKey === "Select_All") {
  //         if ((currentPermissions & bitValue) === bitValue) {
  //           // ✅ If "Select_All" is checked, we save the current permissions
  //           setPreviousPermissions((prevPermissions) => ({
  //             ...prevPermissions,
  //             [action.name]: currentPermissions
  //           }));

  //           // ✅ Force update state to reset all permissions to 0
  //           newPermissions = 0;
  //         } else {
  //           // ✅ If "Select_All" is checked again, restore previous permissions OR full permissions
  //           newPermissions = previousPermissions[action.name] || permissionMapping["Select_All"];
  //         }
  //       } else {
  //         // ✅ Toggle specific permissions (Read, Write, Update, Delete)
  //         newPermissions =
  //           (currentPermissions & bitValue) === bitValue
  //             ? currentPermissions & ~bitValue // Remove the specific permission
  //             : currentPermissions | bitValue; // Add the specific permission
  //       }

  //       console.log(`Updating ${action.name}:`, newPermissions);

  //       return {
  //         ...prev,
  //         [action.name]: newPermissions, // ✅ Ensures state updates correctly
  //       };
  //     });
  // };

  const handlePermissionToggle = (action, permissionKey) => {
    console.log('🟡 DEBUG: Action object received ->', action);
    console.log('🟡 DEBUG: Permission key ->', permissionKey);

    setUpdatedPermissions((prev) => {
      console.log('🟢 PREVIOUS STATE:', prev);
      const currentPermissions = prev[action.name] ?? action.permissions;
      console.log('🔵 Current permissions for', action.name, ':', currentPermissions);

      const bitValue = permissionMapping[permissionKey];

      let newPermissions;

      if (permissionKey === 'Select_All') {
        if ((currentPermissions & bitValue) === bitValue) {
          console.log("🛑 'Select All' is checked, setting permissions to 0");
          setPreviousPermissions((prevPermissions) => ({
            ...prevPermissions,
            [action.name]: currentPermissions
          }));
          newPermissions = 0;
        } else {
          console.log("✅ 'Select All' clicked again, restoring previous permissions");
          newPermissions = previousPermissions[action.name] || permissionMapping['Select_All'];
        }
      } else {
        newPermissions =
          (currentPermissions & bitValue) === bitValue
            ? currentPermissions & ~bitValue // Remove the specific permission
            : currentPermissions | bitValue; // Add the specific permission
      }

      console.log('🔵 New permissions for', action.name, ':', newPermissions);

      return {
        ...prev,
        [action.name]: newPermissions // ✅ Ensure action.name exists
      };
    });
  };

  useEffect(() => {
    console.log('Updated Permissions State:', updatedPermissions);
  }, [updatedPermissions]);

  const [roleType, setRoleType] = useState('');
  console.log('selected role', roleType);
  const [selectedMenu, setSelectedMenu] = useState(null);

  console.log('selected Menu', selectedMenu);
  const [storedData, setStoredData] = useState();
  console.log('storeddata', storedData);
  const navigate = useNavigate();
  const inputRef = useRef(null);

  const roleOptions = levels.map((role) => ({
    value: role.id,
    label: role.role_name
    // id: client.id,
  }));

  // const handleRoleChange = (event) => {
  //   const selectedRole = event.target.value;
  //   setRoleType(selectedRole);

  // Update selectedMenu based on the role type dynamically
  // const roleMenu = roleData[selectedRole];
  // console.log("Role Mennu", roleMenu)
  // setSelectedMenu(roleMenu ? roleMenu[0] : null);

  // setStoredData(roleMenu || []);
  // };
  const [selectedRole, setSelectedRole] = useState('');
  const [selectedRoleType, setSelectedRoleType] = useState(null);

  const [selectedParentRole, setSelectedParentRole] = useState(' ');

  console.log('selected Role', selectedRole.Permissions);

  const handleRoleChange = (selectedValue) => {
    console.log('this is event ', selectedValue);
    const selectedRole = levels.find((role) => role.id === selectedValue);
    // console.log("selected Role", selectedRole);
    if (selectedRole) {
      setSelectedRole(selectedRole);
      setSelectedMenu(selectedRole.Permissions[0]);
    }
  };

  const handleParentRoleChange = (selectedValue) => {
    console.log('this is parent rolee **************** ', selectedValue);
    const selectedRole = parentRole.find((role) => role.id === selectedValue);
    console.log('selected Role ***********', selectedRole.id);
    if (selectedRole) {
      setSelectedParentRole(selectedRole);
    }
  };

  useEffect(() => {
    if (selectedRole) {
      console.log('Updated Role:', selectedRole);
      setValue('description', selectedRole.description || '');
      // setValue(selectedRole.ParentRole.role_name|| "")
      setValue(selectedRole.level || '');
    }
  }, [selectedRole, setValue]);
  useEffect(() => {
    setLoading(false); // Disable loading after initial mount
  }, []);
  const onSubmit = async (data) => {
    // ✅ Ensure this function is async
    console.log('Onsubmit Working');
    if (!selectedRole?.Permissions || selectedRole?.Permissions.length === 0) {
      console.error('❌ Error: Permissions data is missing or empty.');
      return;
    }

    navigate('/user-management/roles');

    // Ensure `updatedPermissions` is correctly handled
    // const updatedMenuStructure = selectedRole?.Permissions.map((menu) => ({
    //   ...menu,
    //   children: (menu.children || []).map((section) => ({
    //     ...section,
    //     children: (section.children || []).map((action) => ({
    //       ...action,
    //       permissions:  updatedPermissions[action.name] !== undefined
    //         ? updatedPermissions[action.name] // ✅ Use latest updatedPermissions
    //         : action.permissions, // ✅ Fallback to existing permissions
    //     })),
    //   })),
    // }));

    const updatedMenuStructure = selectedRole?.Permissions.map((menu) => ({
      ...menu,
      children: (menu.children || []).map((section) => {
        console.log(`🔍 Updating section: ${section.name}`);
        console.log('🔄 Updated permission value:', updatedPermissions[section.name]);

        return {
          ...section,
          permissions: updatedPermissions.hasOwnProperty(section.name) // ✅ Ensure section permissions are updated
            ? updatedPermissions[section.name]
            : section.permissions, // ✅ Fallback to existing permissions
          children: (section.children || []).map((action) => {
            console.log(`🔍 Updating action: ${action.name} under section: ${section.name}`);
            console.log('🔄 Updated action permission value:', updatedPermissions[action.name]);

            return {
              ...action,
              permissions: updatedPermissions.hasOwnProperty(action.name) // ✅ Ensure action permissions are updated
                ? updatedPermissions[action.name]
                : action.permissions // ✅ Fallback to existing permissions
            };
          })
        };
      })
    }));

    const finalData = {
      role_name: data.role_name,
      description: data.description,
      parent_role_id: selectedParentRole.id,
      // parent_role_id: selectedRole.parent_role_id,
      level_code: selectedRole.level,
      permissions: updatedMenuStructure
    };

    console.log('✅ Final Data Submitted:', finalData);

    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');

      const response = await axios.post(`${API_URL}/roles/create-role`, finalData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Response:', response);

      // ✅ Show success Snackbar
      setSnackbarMessage('Role created successfully!');
      setSnackbarSeverity('success');
      setOpenSnackbar(true);
      setLoading(false);

      // ✅ Redirect
      setTimeout(() => {
        navigate('/user-management/roles');
      }, 2000);
    } catch (error) {
      console.error('❌ Error creating role:', error);

      // ⛔ Extract user-friendly error message
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to create role.';

      // ⛔ Show error Snackbar
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset(); // Resets the form fields
    console.log('Form Reset');
  };

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
        <Button variant="outlined" size="small" component={Link} to="/user-management/roles">
          Cancel
        </Button>
        <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
          Submit
        </Button>
      </Stack>
    </Box>
  );
  const formatName = (name) => name.replace(/_/g, ' ');
  const [roleSelection, setRoleSelection] = useState('');
  const filteredRoleOptions = useMemo(() => {
    console.log('Filtering Role Options based on Role Selection:', roleSelection);
    console.log('Original Role Options:', roleOptions);

    if (roleSelection === 'Yes') {
      return roleOptions.filter((option) => option.label.toLowerCase() !== 'super admin'); // Ensure exact match
    }

    return roleOptions;
  }, [roleSelection, roleOptions]);

  const roleSelections = watch('Role_selection');

  const [parentRole, setParentRole] = useState([]);
  console.log('this is parentrole', parentRole);

  useEffect(() => {
    const fetchParentLevels = async () => {
      const token = localStorage.getItem('serviceToken');

      if (!token) {
        console.error('❌ No token found in localStorage. User may not be logged in.');
        return;
      }

      try {
        const res = await axios.get(`${API_URL}/roles/level1`, {
          headers: {
            Authorization: `Bearer ${token}` // ✅ Attach token to request
          }
        });

        console.log('✅ Response Data:', res.data);
        // console.log("🔍 User Level:", userLevel);

        // ✅ Ensure userAllowedLevels is correctly defined
        // const userAllowedLevels = res.data.find((l) => l.level === userLevel)?.can_create || [];
        // console.log("🚀 User Allowed Levels:", userAllowedLevels);

        // ✅ Ensure levels is correctly filtered
        setParentRole(res.data);
      } catch (error) {
        console.error('❌ Error fetching levels:', error);

        if (error.response) {
          console.error('🚨 Response Status:', error.response.status);
          console.error('🚨 Response Data:', error.response.data);

          if (error.response.status === 401) {
            console.error('❌ Unauthorized: Invalid or expired token.');
            alert('Session expired. Please log in again.');
            // Optionally redirect to login page:
            // window.location.href = "/login";
          }
        }
      }
    };

    fetchParentLevels();
  }, []);
  const parentRoleOptions = parentRole.map((role) => ({
    value: role.id,
    label: role.role_name
    // id: client.id,
  }));

  const { user } = useContext(JWTContext);

  // console.log("this is state ***********************", user.roles[0].level_name)
  const userLevel = user?.role?.level_name;
  console.log('this is state ***********************', userLevel);

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          {/* <Spinner /> */}
        </Box>
      ) : (
        <>
          {/* <CustomCardHeader title="Add Roles" secondary={secondaryActions1} /> */}
          <CardActions
            sx={{
              position: 'sticky',
              top: 0, // ✅ Always stick at top
              bgcolor: 'background.default',
              zIndex: 1100, // ✅ Higher than tabs
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                Add Roles
              </Typography>
              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                <Button variant="outlined" size="small" component={Link} to="/user-management/roles">
                  Cancel
                </Button>
                <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
                  Submit
                </Button>
              </Stack>
            </Stack>
          </CardActions>
          <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <MainCard sx={{ borderRadius: '0px', backgroundColor: 'transparent', border: 'none' }}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        <Stack spacing={1}>
                          <InputLabel>
                            Role Name <span style={{ color: 'red' }}>*</span>
                          </InputLabel>
                          <CustomNameField
                            name="role_name"
                            control={control}
                            placeholder="Enter Role Name"
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        </Stack>
                      </Grid>

                      {/* <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
              <Stack spacing={ 1}>
          <InputLabel>Do you want to create a Superadmin?</InputLabel>
          <CustomDropdownField
                name="Role_selection"
                value={roleSelection}
                onChange={(e) => setRoleSelection(e.target.value)}
                control={control}
                placeholder="Select"
                options={[{ value: 'Yes' }, { value: 'No' }]}
                sx={{ backgroundColor: "rgba(248, 249, 250, 1)" }}
              />
        </Stack>
            </Grid>


            {roleSelection === "Yes" && (
              <>
                <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                    <Stack spacing={ 1}>
                        <InputLabel>Parent Role Type</InputLabel>
                        <CustomDropdownFields
                            name="Level_name"
                            control={control}
                            placeholder="Select Client"
                            options={parentRoleOptions}
                            onChange={(e) => handleParentRoleChange(e.target.value)}
                            sx={{backgroundColor: "rgba(248, 249, 250, 1)"}}
                        />

                    </Stack>
                </Grid>
                </>

            )} */}
                      {parseInt(userLevel.replace('Level ', '')) <= 0 && (
                        <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                          <Stack spacing={1}>
                            <InputLabel>Do you want to create a Admin?</InputLabel>
                            <CustomDropdownField
                              name="Role_selection"
                              value={roleSelection}
                              onChange={(e) => setRoleSelection(e.target.value)}
                              control={control}
                              placeholder="Select"
                              options={[{ value: 'Yes' }, { value: 'No' }]}
                              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            />
                          </Stack>
                        </Grid>
                      )}

                      {roleSelection === 'Yes' && parseInt(userLevel.replace('Level ', '')) <= 0 && (
                        <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                          <Stack spacing={1}>
                            <InputLabel>Parent Role Type</InputLabel>
                            <CustomDropdownFields
                              name="Level_name"
                              control={control}
                              placeholder="Select Client"
                              options={parentRoleOptions}
                              onChange={(e) => handleParentRoleChange(e.target.value)}
                              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            />
                          </Stack>
                        </Grid>
                      )}

                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        <Stack spacing={1}>
                          <InputLabel>Role Type</InputLabel>
                          <CustomDropdownFields
                            name="Level_name"
                            control={control}
                            placeholder="Select Client"
                            options={filteredRoleOptions}
                            onChange={(e) => handleRoleChange(e.target.value)}
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        </Stack>
                      </Grid>

                      {selectedRole && (
                        <>
                          {/* <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                    <Stack spacing={ 1}>
                    <InputLabel>Parent Role</InputLabel>
                    <CustomNameField name="Parent Role" control={control} defaultValue={selectedRole.ParentRole.role_name || ""} placeholder="Enter Parent Role"  sx={{backgroundColor: "rgba(248, 249, 250, 1)"}} />
                    </Stack>
                    </Grid>
                  */}

                          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                            <Stack spacing={1}>
                              <InputLabel>Description</InputLabel>
                              <CustomNameField
                                name="description"
                                control={control}
                                defaultValue={selectedRole.description || ''}
                                placeholder="Enter Description"
                                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                              />
                            </Stack>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </MainCard>
                </Grid>
                {selectedRole && (
                  <>
                    <Grid item xs={12} lg={2} md={3} sm={4}>
                      <Grid Container spacing={3}>
                        <MainCard sx={{ borderRadius: '0px' }}>
                          {selectedRole?.Permissions?.map((page, pageIndex) => {
                            // console.log(`Menu Index: ${data1}`); // Log the menu object and its index
                            console.log(`Page Index: ${pageIndex}`, page); // Log the page object and its index

                            return (
                              <List
                                key={pageIndex}
                                component="nav"
                                sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}
                              >
                                <ListItemButton
                                  onClick={() => setSelectedMenu(page)}
                                  sx={{
                                    backgroundColor: selectedMenu?.name === page.name ? 'rgba(0, 0, 0, 0.08)' : 'transparent', // Fix hover for selected item
                                    '&:hover': {
                                      backgroundColor: 'rgba(0, 0, 0, 0.08)', // Background color on hover
                                      color: 'secondary.main', // Text color on hover
                                      borderRadius: '4px' // Optional: Add border-radius for hover effect
                                    }
                                  }}
                                >
                                  <ListItemIcon>
                                    {page.name === 'Left_Menu' && <MenuIcon />}
                                    {page.name === 'Top_Menu' && <ManageAccountsIcon />}
                                  </ListItemIcon>
                                  <ListItemText primary={page.name.replace('_', ' ')} />
                                </ListItemButton>
                              </List>
                            );
                          })}
                        </MainCard>
                      </Grid>
                    </Grid>

                    {/* <Grid item xs={12} xl ={10} lg={8} md={9} sm={8}>
            <TableContainer component={Paper} sx={{ borderRadius: "0px" }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ height: "20px", backgroundColor: "white" }}>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem" ,fontFamily: "Roboto", color: "rgb(117, 113, 113)",  }}>
                      Actions
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Read
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold" , color: "rgb(117, 113, 113)"}}>
                      Write
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Update
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Delete
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Select All
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {selectedMenu?.children?.map((section, sectionIndex) => (
                    <React.Fragment key={sectionIndex}>
                    
                      <TableRow sx={{ height: "30px" }}>
                         
                       
                  
                        <TableCell
                          colSpan={6}
                          sx={{
                            padding: "4px",
                            backgroundColor: "#f5f5f5",
                            fontWeight: "bold",
                            lineHeight: "1",
                            textAlign: "left",
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontSize: "0.875rem",
                              display: "flex",
                              alignItems: "center",
                              paddingLeft: "10px",
                            }}
                          >
                               <Checkbox
                                  checked={isPermissionEnabled(updatedPermissions[section.name] ?? section.permissions, "Select_All")}
                                  onChange={() => handlePermissionToggle(section, "Select_All")}
                                  sx={{ marginRight: "0px" }}
                                />

                             
                            
                            <span style={{ marginRight: "8px", marginTop: "3px" }}>
                              {section.name === "Jobs management" ? (
                                <WorkIcon fontSize="small" />
                              ) : (
                                <GroupIcon fontSize="small" />
                              )}
                            </span>
                            {section.name}   
                          </Typography>
                        </TableCell>
                       
                      
                      </TableRow>
                  
                      {section?.children?.map((action, actionIndex) => {
                          console.log("Action Index:", actionIndex); // Logs the index of the current action
                          console.log("Action Object:", action); // Logs the action object being rendered
                          console.log("Section Object:", section); // Logs the action object being rendered
                          return (
                            <TableRow key={actionIndex} sx={{ height: "32px" }}>
                              <TableCell sx={{ padding: "10px", fontSize: "0.875rem", border: 0 }}>
                                {action.name.replaceAll("_", " ")}
                              </TableCell>
                              {Object.keys(permissionMapping).map((permissionKey) => (
                                <TableCell
                                  align="center"
                                  key={permissionKey}
                                  sx={{ padding: "10px", border: 0 }}
                                >
                              
                              <Checkbox 
                                    checked={isPermissionEnabled(updatedPermissions[action.name] ?? action.permissions, permissionKey)}
                                    onChange={() => handlePermissionToggle(action, permissionKey)}
                                  />
                                     
                                  
                                </TableCell>
                              ))}
                            </TableRow>
                          );
                        })}

                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

        </Grid> */}

                    <Grid item xs={12} xl={10} lg={8} md={9} sm={8}>
                      <TableContainer component={Paper} sx={{ borderRadius: '0px' }}>
                        <Table>
                          <TableHead>
                            <TableRow sx={{ height: '20px', backgroundColor: 'white' }}>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontFamily: 'Roboto', color: 'rgb(117, 113, 113)' }}
                              >
                                Actions
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                              >
                                Read
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                              >
                                Write
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                              >
                                Update
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                              >
                                Delete
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                              >
                                Select All
                              </TableCell>
                            </TableRow>
                          </TableHead>

                          {/* <TableBody>
                      {selectedMenu?.children?.map((section, sectionIndex) => (
                        <React.Fragment key={sectionIndex}>
                          <TableRow sx={{ height: '30px' }}>
                            <TableCell
                              colSpan={6}
                              sx={{
                                padding: '4px',
                                backgroundColor: '#f5f5f5',
                                fontWeight: 'bold',
                                lineHeight: '1',
                                textAlign: 'left'
                              }}
                            >
                              <Typography
                                variant="h6"
                                sx={{
                                  fontSize: '0.875rem',
                                  display: 'flex',
                                  alignItems: 'center',
                                  paddingLeft: '10px'
                                }}
                              >
                                {section.name}
                              </Typography>
                            </TableCell>

                            {Object.keys(permissionMapping).map((permissionKey) => (
                              <TableCell align="center" key={permissionKey} sx={{ padding: '10px', border: 0 }}>
                                <Checkbox
                                  checked={isPermissionEnabled(updatedPermissions[section.name] ?? section.permissions, permissionKey)}
                                  onChange={() => handlePermissionToggle(section, permissionKey)}
                                />
                              </TableCell>
                            ))}
                          </TableRow>
                        </React.Fragment>
                      ))}
                    </TableBody> */}
                          <TableBody>
                            {selectedMenu?.children?.map((section, sectionIndex) => {
                              return (
                                <TableRow key={sectionIndex} sx={{ height: '30px', backgroundColor: '#f5f5f5' }}>
                                  {/* Section Name */}
                                  <TableCell sx={{ padding: '4px', fontWeight: 'bold', lineHeight: '1', textAlign: 'left' }}>
                                    <Typography variant="h6" sx={{ fontSize: '0.875rem', paddingLeft: '10px' }}>
                                      {section.name}
                                    </Typography>
                                  </TableCell>

                                  {/* Permissions Checkboxes */}
                                  {Object.keys(permissionMapping).map((permissionKey) => (
                                    <TableCell align="center" key={permissionKey} sx={{ padding: '10px', border: 0 }}>
                                      <Checkbox
                                        checked={isPermissionEnabled(
                                          updatedPermissions[section.name] ?? section.permissions,
                                          permissionKey
                                        )}
                                        onChange={() => handlePermissionToggle(section, permissionKey)}
                                      />
                                    </TableCell>
                                  ))}

                                  {/* Select All Checkbox */}
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>
                  </>
                )}
                ;
              </Grid>

              {/* Sticky Dialog Actions - Place this just before closing the form */}
            </form>
          </MainCard>
          <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
            <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </>
      )}
    </>
  );
};

export default AddRoles;
