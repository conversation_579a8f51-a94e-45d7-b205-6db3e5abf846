import React from 'react';
import PropTypes from 'prop-types';
import { Typography, Chip, Tooltip } from '@mui/material';

// ✅ Helper function for status mapping
const getStatusInfo = (isActive) => {
  if (isActive === true) return { label: 'Active', color: 'success' };
  if (isActive === false) return { label: 'Inactive', color: 'error' };
  return { label: 'Unknown', color: 'warning' };
};

// ✅ Generic tooltip cell for text values
const TooltipTextCell = ({ value, link }) => {
  const content = (
    <Typography
      variant="body2"
      component={link ? 'a' : 'span'}
      href={link ? `mailto:${value}` : undefined}
      sx={{
        display: 'inline-block',
        ...(link && {
          color: '#1976d2',
          textDecoration: 'none',
          cursor: 'pointer',
        }),
      }}
      noWrap
    >
      {value}
    </Typography>
  );

  return (
    <Tooltip title={value} placement="bottom" arrow>
      <span>{content}</span>
    </Tooltip>
  );
};

TooltipTextCell.propTypes = {
  value: PropTypes.string.isRequired,
  link: PropTypes.bool,
};

// ✅ Specific cell wrappers (with propTypes defined)
export const UserNameCell = ({ value }) => <TooltipTextCell value={value} />;
UserNameCell.propTypes = { value: PropTypes.string.isRequired };

export const EmailCell = ({ value }) => <TooltipTextCell value={value} link />;
EmailCell.propTypes = { value: PropTypes.string.isRequired };

export const FirstNameCell = ({ value }) => <TooltipTextCell value={value} />;
FirstNameCell.propTypes = { value: PropTypes.string.isRequired };

export const LastNameCell = ({ value }) => <TooltipTextCell value={value} />;
LastNameCell.propTypes = { value: PropTypes.string.isRequired };

export const RoleNameCell = ({ value }) => <TooltipTextCell value={value} />;
RoleNameCell.propTypes = { value: PropTypes.string.isRequired };

export const ParentRoleCell = ({ value }) => <TooltipTextCell value={value} />;
ParentRoleCell.propTypes = { value: PropTypes.string.isRequired };

// ✅ Status cell with chip
export const StatusCell = ({ value }) => {
  const { label, color } = getStatusInfo(value);
  return (
    <Tooltip title={label} placement="bottom" arrow>
      <Chip color={color} label={label} size="small" variant="outlined" />
    </Tooltip>
  );
};
StatusCell.propTypes = { value: PropTypes.bool.isRequired };
