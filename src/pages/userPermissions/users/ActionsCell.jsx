// 📁 components/ActionsCell.jsx
import React from 'react';
import PropTypes from 'prop-types';
import ActionsCellBase from 'components/common/ActionsCellBase';

const resolveUserPayload = (row) => {
  return row.original?.user ? row.original : row.original?.original || row.original;
};

const ActionsCell = ({ row, navigate, handleDeleteClick }) => (
  <ActionsCellBase
    row={row}
    navigate={navigate}
    handleDeleteClick={handleDeleteClick}
    editPath="/user-management/edit-users"
    resolvePayload={resolveUserPayload}
  />
);

ActionsCell.propTypes = {
  row: PropTypes.object.isRequired,
  navigate: PropTypes.func.isRequired,
  handleDeleteClick: PropTypes.func.isRequired
};

export default ActionsCell;
