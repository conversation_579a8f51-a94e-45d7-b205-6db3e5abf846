import React, { useState, useContext } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
// RHF is handled by useEntityForm
import { Button, Grid, Snackbar, Typography, Stack, CardActions } from '@mui/material';
import MuiAlert from '@mui/material/Alert';
import { useTheme } from '@mui/material/styles';
import DialogData from '../roles/DialogData';
import JWTContext from 'contexts/JWTContext';
import MainCard from 'components/MainCard';
import { MOCK_SITE_MAP_DATA, MOCK_TENANTS_LIST } from '../shared/mockData';
import TenantSelector from 'components/userPermissions/TenantSelector';
import NameDescriptionFields from 'components/userPermissions/NameDescriptionFields';
import { useEntityForm } from 'hooks/useEntityForm';

const AddUsersPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { user } = useContext(JWTContext);

  const editData = location.state?.data;
  const {
    control,
    handleSubmit,
    formState: { errors },
    initialSiteMapData,
    setInitialSiteMapData,
    tenants,
    tenantId,
    setTenantId,
    isSubmitting,
    setIsSubmitting
  } = useEntityForm({ initialSiteMap: MOCK_SITE_MAP_DATA, tenantsList: MOCK_TENANTS_LIST, user, editData });

  // Use shared mock constants

  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);
  // Initialized by useEntityForm

  const handleSuccess = () => {
    setSnackbarMessage('User updated successfully!');
    setSnackbarSeverity('success');
    setOpenSnackbar(true);
    setTimeout(() => {
      navigate('/user-management/users');
    }, 2000);
  };

  const handleFailure = (err) => {
    let message;
    if (err?.message?.toLowerCase().includes('user already exist')) {
      message = 'A user with this name already exists. Please choose a different name.';
    } else if (err?.message?.toLowerCase().includes('name is mandatory')) {
      message = 'User name cannot be empty or contain only spaces.';
    } else {
      message = 'Failed to update User. Please try again later.';
    }
    setSnackbarMessage(message);
    setSnackbarSeverity('error');
    setOpenSnackbar(true);
  };

  async function onSubmit(formData) {
    const fields = {
      name: formData?.name,
      description: formData?.description,
      orgId: user?.organisationCategory === 'Chidhagni' ? tenantId : user?.orgId,
      permissions: initialSiteMapData
    };

    setIsSubmitting(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log('User updated with data:', fields);
      handleSuccess();
    } catch (error) {
      console.error('User Update failed:', error);
      handleFailure(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <CardActions
        sx={{
          position: 'sticky',
          top: 0,
          bgcolor: 'background.default',
          zIndex: 1100,
          borderBottom: '1px solid',
          borderBottomColor: theme.palette.divider,
          padding: '8px 16px'
        }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
          <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
            Update User
          </Typography>
          <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
            <Button variant="outlined" size="small" component={Link} to="/user-management/users">
              Cancel
            </Button>
            <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update User'}
            </Button>
          </Stack>
        </Stack>
      </CardActions>

      <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}>
              <TenantSelector
                control={control}
                tenantsList={tenants}
                tenantId={tenantId}
                setTenantId={setTenantId}
                editOrgId={editData?.orgId}
                errors={errors}
                placeholder="Select"
              />
            </Grid>

            <NameDescriptionFields control={control} nameDefault={editData?.name || ''} nameLabel="User Name" asGridItems />

            <Grid item xs={12}>
              <DialogData initialSiteMapData={initialSiteMapData} setInitialSiteMapData={setInitialSiteMapData} />
            </Grid>
          </Grid>
        </form>
      </MainCard>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={() => setOpenSnackbar(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MuiAlert onClose={() => setOpenSnackbar(false)} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
    </>
  );
};

export default AddUsersPage;
