import React, { useState, useMemo, useEffect, useContext } from 'react';
import { Box, Typography, IconButton, useMediaQuery, CircularProgress, MenuItem, Menu } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import SettingsIcon from '@mui/icons-material/Settings';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import DownloadIcon from '@mui/icons-material/Download';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import DeleteIcon from '@mui/icons-material/Delete';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import CustomTableContainer from 'components/custom-components/CustomTableContainer';
import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import data from './staticdata';
import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
import { CSVExport, DebouncedInput } from 'components/third-party/react-table';
import canMenuPage from 'pages/permissions/RBACContext';
import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS } from 'constants';
import Chip from '@mui/material/Chip';
import Tooltip from '@mui/material/Tooltip';
import ConfirmDeleteDialog from 'pages/jobrequest/jorequestdeletedialog';
import Spinner from 'components/custom-components/Spinner';

const API_URL = import.meta.env.VITE_APP_API_URL;

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  // rank the item
  const itemRank = rankItem(row.getValue(columnId), value);

  // store the ranking info
  addMeta(itemRank);

  // return if the item should be filtered in/out
  return itemRank.passed;
};

const ParentComponent = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { canMenuPage } = useRBAC();
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const canAddUsers = canMenuPage('Top_Menu', 'Users', PERMISSIONS.WRITE);
  const canEditUsers = canMenuPage('Top_Menu', 'Users', PERMISSIONS.EXECUTE);
  const canDeleteUsers = canMenuPage('Top_Menu', 'Users', PERMISSIONS.DELETE);
  const [clientId, setClientId] = useState(null);
  console.log('Add Users', canAddUsers);
  const handleDeleteClick = (rowData) => {
    setClientId(rowData.id);
    setIsActive(rowData.is_active);
    setConfirmDeleteOpen(true);
  };

  const [data, setData] = useState([]);
  console.log('Fetched user roles data:', data);
  const handleConfirmDelete = async () => {
    try {
      const payload = {
        is_active: !isActive
      };
      const response = await fetch(`${API_URL}/individualsRoles/toggle-user-role/${clientId}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('serviceToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      fetchUserRolesRequests();
    } catch (error) {
      console.error('Error updating role status:', error);
    } finally {
      setConfirmDeleteOpen(false);
    }
  };
  const [loading, setLoading] = useState(true);

  const fetchUserRolesRequests = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/individualsRoles/get-users-with-roles`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      console.log('this is jsonrequest', response);
      const data = await response.json();
      setTimeout(() => {
        setData(data);
        setLoading(false); // Stop loading
      });
    } catch (error) {
      console.error('Error fetching job requests:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserRolesRequests();
  }, []);

  // Define columns

  const columns = useMemo(() => {
    const baseColumns = [
      {
        header: 'User Name',
        accessorKey: 'user.username',
        cell: ({ getValue }) => {
          const userName = getValue();
          return (
            <Tooltip title={userName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {userName}
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: "Email",
      //   accessorKey: "user.email",
      //   cell: ({ getValue }) => {
      //     const userEmail = getValue();
      //     return (
      //       <Tooltip title={userEmail} placement="bottom">
      //         <Typography variant="body2" sx={{ display: "inline-block" }}>
      //           {userEmail}
      //         </Typography>
      //       </Tooltip>
      //     );
      //   },
      // },

      {
        header: 'Email',
        accessorKey: 'user.email',
        cell: ({ getValue }) => {
          const emailId = getValue();
          return (
            <Tooltip title={emailId} placement="bottom">
              <Typography
                variant="body2"
                component="a"
                href={`mailto:${emailId}`}
                sx={{
                  display: 'inline-block',
                  color: '#1976d2', // link blue
                  textDecoration: 'none',
                  cursor: 'pointer'
                }}
              >
                {emailId}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'First Name',
        accessorKey: 'user.first_name',
        cell: ({ getValue }) => {
          const firstName = getValue();
          return (
            <Tooltip title={firstName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {firstName}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Last Name',
        accessorKey: 'user.last_name',
        cell: ({ getValue }) => {
          const lastName = getValue();
          return (
            <Tooltip title={lastName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {lastName}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Role',
        accessorKey: 'role.role_name',
        cell: ({ getValue }) => {
          const roleName = getValue();
          return (
            <Tooltip title={roleName} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {roleName}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Parent Role',
        accessorKey: 'role.ParentRole.role_name',
        cell: ({ getValue }) => {
          const parentRole = getValue();
          return (
            <Tooltip title={parentRole} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {parentRole}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Status',
        accessorKey: 'is_active',
        cell: ({ getValue }) => {
          const isActive = getValue();
          const statusLabel = isActive === true ? 'Active' : isActive === false ? 'Inactive' : 'Unknown';
          const chipColor = isActive === true ? 'success' : isActive === false ? 'error' : 'warning';
          return (
            <Tooltip title={statusLabel} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                <Chip color={chipColor} label={statusLabel} size="small" variant="light" />
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: "Actions",
      //   cell: ({ row }) => {
      //     return (
      //       <Box>
      //         {canEditUsers && (
      //           <Tooltip title="Edit" placement="bottom">
      //             <IconButton
      //               sx={{
      //                 padding: 0.2, // Reduce padding
      //                 height: 24, // Reduce height
      //                 width: 24, // Reduce width to make it smaller
      //                 minWidth: "unset", // Remove minimum width restriction
      //               }}
      //               onClick={() => {
      //                 console.log("Row data:", row);
      //                 navigate("/user-management/edit-users", {
      //                   state: { row: JSON.parse(JSON.stringify(row)) },
      //                 });
      //               }}
      //             >
      //               <ModeEditIcon />
      //             </IconButton>
      //           </Tooltip>
      //         )}

      //         {canDeleteUsers && (
      //           <Tooltip title="Delete" placement="bottom">
      //             <IconButton
      //               sx={{
      //                 padding: 0.2, // Reduce padding
      //                 height: 24, // Reduce height
      //                 width: 24, // Reduce width to make it smaller
      //                 minWidth: "unset", // Remove minimum width restriction
      //               }}
      //               onClick={() => handleDeleteClick(row.original)}
      //               style={{ color: row.original.is_active ? "red" : "gray" }}
      //             >
      //               <DeleteIcon />
      //             </IconButton>
      //           </Tooltip>
      //         )}
      //       </Box>
      //     );
      //   },
      // },
      {
        header: 'Actions',
        id: 'actions',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);

          const handleMenuClick = (event) => {
            event.stopPropagation(); // Prevent row selection on click
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          return (
            <>
              <IconButton onClick={handleMenuClick}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
              >
                <MenuItem
                  onClick={() => {
                    console.log('Row data:', row);
                    navigate('/user-management/edit-users', {
                      state: { row: JSON.parse(JSON.stringify(row)) }
                    });
                  }}
                >
                  Edit
                </MenuItem>
                {/* <MenuItem onClick={() => navigate('/leads/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}>View</MenuItem> */}
                <MenuItem onClick={() => handleDeleteClick(row.original)}>Delete</MenuItem>
              </Menu>
            </>
          );
        }
      }
    ];

    return baseColumns;
  }, [canEditUsers, canDeleteUsers]);

  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [originalData, setOriginalData] = useState(() => [...data]);

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    // onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    // onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: (value) => {
      console.log('🔍 onGlobalFilterChange triggered:', value); // Debugging log
      setGlobalFilter(value);
    },
    globalFilterFn: fuzzyFilter,
    onRowSelectionChange: setRowSelection,
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      },
      revertData: (rowIndex, revert) => {
        if (revert) {
          setData((old) => old.map((row, index) => (index === rowIndex ? originalData[rowIndex] : row)));
        } else {
          setOriginalData((old) => old.map((row, index) => (index === rowIndex ? data[rowIndex] : row)));
        }
      }
    }
  });

  let headers = [];
  table.getVisibleLeafColumns().map(
    (columns) =>
      // @ts-ignore
      columns.columnDef.accessorKey &&
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        // @ts-ignore
        key: columns.columnDef.accessorKey
      })
  );

  useEffect(() => setColumnVisibility({ id: false, active: true }), []);
  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey) // ✅ Keep only columns with data keys
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean); // ✅ Clean out undefined/empty headers
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000); // ⏱ change every 2 seconds (you can adjust)

    return () => clearInterval(interval); // Cleanup
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const secondaryActions2 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      {/* <CSVExport
                        {...{
                          data:
                            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
                              ? data
                              : table.getSelectedRowModel().flatRows.map((row) => row.original),
                          headers,
                          filename: 'Users.csv'
                        }}
                      /> */}
      {canAddUsers && (
        <IconButton
          onClick={() => navigate('/user-management/add-users')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'primary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
    </Box>
  );

  const secondaryActions3 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <ColumnVisibilitySelector table={table} sx={{ padding: '5px' }} />
      {/* <CSVExport
                        {...{
                          data:
                            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
                              ? data
                              : table.getSelectedRowModel().flatRows.map((row) => row.original),
                          headers,
                          filename: 'Users.csv'
                        }}
                      /> */}
      {canAddUsers && (
        <IconButton
          onClick={() => navigate('/user-management/add-users')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'primary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  const paginationButton = (
    <TablePagination
      {...{
        setPageSize: table.setPageSize,
        setPageIndex: table.setPageIndex,
        getState: table.getState,
        getPageCount: table.getPageCount
      }}
    />
  );

  const rowButton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  return (
    <>
      <ConfirmDeleteDialog
        open={confirmDeleteOpen}
        handleClose={() => setConfirmDeleteOpen(false)}
        handleConfirm={handleConfirmDelete}
        isActive={isActive}
        entityType="UserRole"
      />

      <CustomTableContainer
        table={table}
        onAddClick={() => navigate('/user-management/add-users')}
        csvFilename="Users-list.csv"
        showAddButton={canAddUsers}
        addLabel="User"
        data={data}
        rowSelection={rowSelection}
        theme={theme}
      />
    </>
  );
};

export default ParentComponent;
