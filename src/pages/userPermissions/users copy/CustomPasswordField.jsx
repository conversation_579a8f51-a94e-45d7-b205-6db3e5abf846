import React, { useState } from 'react';
import { TextField, FormControl, IconButton, InputAdornment } from '@mui/material';
import { Controller } from 'react-hook-form';
import { Visibility, VisibilityOff } from '@mui/icons-material';

const CustomPasswordField = ({
  name,
  control,
  placeholder = "Enter password",
  defaultValue = "",
  disabled = false,
  sx = {},
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  // Toggle password visibility
  const handleTogglePassword = () => setShowPassword(!showPassword);

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue}
        rules={{
          required: "Password is required",
          minLength: {
            value: 8,
            message: "Password must be at least 8 characters long",
          },
          pattern: {
            value: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]+$/,  // Alphanumeric with special chars
            message: "Password must contain at least one letter and one number",
          },
        }}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            type={showPassword ? "text" : "password"}
            placeholder={placeholder}
            size="small"
            disabled={disabled}
            sx={{
              // borderRadius: '2px',
              // '& fieldset': {
              //   borderRadius: '2px'
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'italic',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '& .MuiFormHelperText-root': {
                backgroundColor: 'white !important',
                padding: '2px 4px',
                margin: 0
              },
              '& .MuiOutlinedInput-root': {
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main'
                }
              },
              ...sx
            }}
            error={Boolean(fieldState?.error)}
            helperText={fieldState?.error?.message}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleTogglePassword} edge="end">
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            {...props}
          />
        )}
      />
    </FormControl>
  );
};

export default CustomPasswordField;
