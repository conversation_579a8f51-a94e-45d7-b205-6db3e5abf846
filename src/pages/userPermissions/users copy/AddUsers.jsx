import React, { useState, useEffect, useContext } from 'react';
import { useForm, Controller } from 'react-hook-form';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import { borderRadius, Box } from '@mui/system';
import { Tab, Checkbox } from '@mui/material';
import { Link } from 'react-router-dom';
import IconButton from '@mui/material/IconButton';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import { useNavigate } from 'react-router-dom';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
// project-imports
import MainCard from 'components/MainCard';
import { useRef } from 'react';
import { Outlet } from 'react-router';
import ProfileTab from 'pages/userPermissions/roles/ProfileTab';
import data from './staticdata';
import CustomCheckbox from '../roles/checkbox';
import WorkIcon from '@mui/icons-material/Work';
import GroupIcon from '@mui/icons-material/Group';
import MenuIcon from '@mui/icons-material/Menu';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import axios from 'axios';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Select, MenuItem } from '@mui/material';
import List from '@mui/material/List';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
import CustomPasswordField from 'pages/userPermissions/users/CustomPasswordField';
import JWTContext from 'contexts/JWTContext';
import Button from '@mui/material/Button';
import { Snackbar, Alert } from '@mui/material';
import CustomUsernameField from 'components/custom-components/CustomUsernameField';
import CardActions from '@mui/material/CardActions';
import { HEADER_HEIGHT } from 'config';
import { useTheme } from '@mui/material/styles';
import Spinner from 'components/custom-components/Spinner';

const API_URL = import.meta.env.VITE_APP_API_URL;

const roleData = {
  Admin: [
    {
      name: 'Left_Menu',
      children: [
        {
          name: 'Client',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 9,
          children: [
            { name: 'Add_Client', type: 'ACTION', permissions: 15, displayNumber: 1 },
            { name: 'Edit', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        }
      ]
    },
    {
      name: 'Top_Menu',
      children: [
        {
          name: 'Users',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 9,
          children: [
            { name: 'Add_Users', type: 'ACTION', permissions: 15, displayNumber: 1 },
            { name: 'Edit', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        },
        {
          name: 'Roles',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 10,
          children: [
            { name: 'Add_Roles', type: 'ACTION', permissions: 2, displayNumber: 1 },
            { name: 'Edit_Roles', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update_Roles', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate_Roles', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        }
      ]
    }
  ],
  SuperAdmin: [
    {
      name: 'Left_Menu',
      children: [
        {
          name: 'Client',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 9,
          children: [
            { name: 'Add_Client', type: 'ACTION', permissions: 15, displayNumber: 1 },
            { name: 'Edit', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        }
      ]
    },
    {
      name: 'Top_Menu',
      children: [
        {
          name: 'Users',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 9,
          children: [
            { name: 'Add_Users', type: 'ACTION', permissions: 15, displayNumber: 1 },
            { name: 'Edit', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        },
        {
          name: 'Roles',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 10,
          children: [
            { name: 'Add_Roles', type: 'ACTION', permissions: 2, displayNumber: 1 },
            { name: 'Edit_Roles', type: 'ACTION', permissions: 15, displayNumber: 2 },
            { name: 'Update_Roles', type: 'ACTION', permissions: 15, displayNumber: 3 },
            { name: 'Deactivate_Roles', type: 'ACTION', permissions: 15, displayNumber: 4 }
          ]
        }
      ]
    }
  ]
};

// s
//   {
//     name: "Left_Menu",
//     children: [
//       {
//         name: "Client",
//         type: "PAGE",
//         permissions: 15,
//         displayNumber: 9,
//         children: [
//           { name: "Add_Client", type: "ACTION", permissions: 15, displayNumber: 1 },
//           { name: "Edit", type: "ACTION", permissions: 15, displayNumber: 2 },
//           { name: "Update", type: "ACTION", permissions: 15, displayNumber: 3 },
//           { name: "Deactivate", type: "ACTION", permissions: 15, displayNumber: 4 },
//         ],
//       },
//       {
//         name: "Employee",
//         type: "PAGE",
//         permissions: 15,
//         displayNumber: 10,
//         children: [
//           { name: "Add_Employee", type: "ACTION", permissions: 2, displayNumber: 1 },
//           { name: "Edit_Employee", type: "ACTION", permissions: 15, displayNumber: 2 },
//           { name: "Update_Employee", type: "ACTION", permissions: 15, displayNumber: 3 },
//           { name: "Deactivate_Employee", type: "ACTION", permissions: 15, displayNumber: 4 },
//         ],
//       },
//     ],
//     permissions: 15,
//     displayNumber: 1,
//   },
//   {
//     name: "Top_Menu",
//     children: [
//       {
//         name: "Users",
//         type: "PAGE",
//         permissions: 15,
//         displayNumber: 9,
//         children: [
//           { name: "Add_Users", type: "ACTION", permissions: 15, displayNumber: 1 },
//           { name: "Edit", type: "ACTION", permissions: 15, displayNumber: 2 },
//           { name: "Update", type: "ACTION", permissions: 15, displayNumber: 3 },
//           { name: "Deactivate", type: "ACTION", permissions: 15, displayNumber: 4 },
//         ],
//       },
//       {
//         name: "Roles",
//         type: "PAGE",
//         permissions: 15,
//         displayNumber: 10,
//         children: [
//           { name: "Add_Roles", type: "ACTION", permissions: 2, displayNumber: 1 },
//           { name: "Edit_Roles", type: "ACTION", permissions: 15, displayNumber: 2 },
//           { name: "Update_Roles", type: "ACTION", permissions: 15, displayNumber: 3 },
//           { name: "Deactivate_Roles", type: "ACTION", permissions: 15, displayNumber: 4 },
//         ],
//       },
//     ],
//     permissions: 15,
//     displayNumber: 1,
//   },
// ];
// Permissions mapping
const permissionMapping = {
  Read: 1, // Binary 0001
  Write: 2, // Binary 0010
  Update: 4, // Binary 0100
  Delete: 8, // Binary 1000
  Select_All: 15 // Full permissions
};

const AddUsers = () => {
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };
  const defaultValues = {
    email: '',
    username: '',
    permissions: {}
  };
  const {
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm({
    defaultValues
  });

  const isPermissionEnabled = (permissions, permissionKey) => {
    const bitValue = permissionMapping[permissionKey];
    return (permissions & bitValue) === bitValue;
  };

  const [updatedPermissions, setUpdatedPermissions] = useState({});

  const handlePermissionToggle = (action, permissionKey) => {
    setUpdatedPermissions((prev) => {
      const currentPermissions = prev[action.name] || action.permissions; // Use current or default permissions
      const bitValue = permissionMapping[permissionKey];

      // Toggle the bit value
      const newPermissions =
        (currentPermissions & bitValue) === bitValue
          ? currentPermissions & ~bitValue // Turn off the permission
          : currentPermissions | bitValue; // Turn on the permission

      console.log('New Permissions:', newPermissions); // Log the updated permissions

      return {
        ...prev,
        [action.name]: newPermissions // Update the permissions for the specific action
      };
    });
  };

  useEffect(() => {
    console.log('Updated Permissions State:', updatedPermissions);
  }, [updatedPermissions]);

  const [roles, setRoles] = useState([]);
  console.log('this is used in roles', roles);

  const [roleType, setRoleType] = useState('');
  console.log('selected role', roleType);
  const [selectedMenu, setSelectedMenu] = useState(null);
  console.log('selected Menu', selectedMenu);
  const [storedData, setStoredData] = useState();
  const [selectedRole, setSelectedRole] = useState(null);
  console.log('storeddata', storedData);
  const navigate = useNavigate();
  const inputRef = useRef(null);

  const { user } = useContext(JWTContext);

  console.log('this is statssssssssse ***********************', user);

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        console.log('It is fetcing ');
        const response = await axios.get(`${API_URL}/roles`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('serviceToken')}`
          }
        });
        console.log('this is repsonse', response.data);
        if (Array.isArray(response.data)) {
          setRoles(response.data);
        } else {
          console.error('Unexpected data format:', response.data);
        }
      } catch (error) {
        console.error('Failed to fetch clients:', error);
      }
    };
    fetchRoles();
  }, []);

  const handleRoleChange = (selectedValue) => {
    console.log('this is selected value ', selectedValue);
    const selectedRoles = roles.find((role) => role.id === selectedValue);
    setSelectedRole(selectedValue);

    console.log('this is seleeted client ', selectedRoles);
  };

  const rolesOptions = roles.map((role) => ({
    value: role.id,
    label: role.role_name
  }));

  console.log('this is roles Options ', rolesOptions);
  useEffect(() => {
    setLoading(false); // Disable loading after initial mount
  }, []);
  const onSubmit = async (data) => {
    // Step 1: Log for debugging
    console.log('Rendering and preparing data to be submitted');

    // Step 2: Combine form data and prepare final payload
    const finalData = {
      username: data.username,
      email: data.email_id, // Use email_id as per your form field
      first_name: data.first_name,
      last_name: data.last_name,
      password: data.password,
      role_id: selectedRole // Assuming selectedRole is an object with the necessary ID
    };

    console.log('✅ Final Data Submitted:', finalData);

    // Step 3: Perform the API request
    try {
      const token = localStorage.getItem('serviceToken');

      const response = await axios.post(`${API_URL}/individualsRoles/create-user`, finalData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Response Data:', response);

      // ✅ Show success Snackbar
      setSnackbarMessage('User created successfully!');
      setSnackbarSeverity('success');
      setOpenSnackbar(true);
      setLoading(false);

      // ✅ Redirect
      setTimeout(() => {
        navigate('/user-management/users');
      }, 2000);
    } catch (error) {
      console.error('❌ Error creating user:', error);

      // ⛔ Extract a user-friendly error message
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to create user.';

      // ⛔ Show error Snackbar
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset(); // Resets the form fields
    console.log('Form Reset');
  };

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      {/* <IconButton
       sx={{ 
        backgroundColor: "primary.main", 
        width: 60, 
        height: 30, 
        borderRadius: '6px', 
        border: '1px solid grey',
        '&:hover': {
          backgroundColor: "primary.main" // Prevent hover effect
        },
        '&:focus': {
          backgroundColor: "primary.main" // Prevent focus effect
        }
      }}
        // component={Link}
        // to="/user-management/users"
        // onClick={handleSubmit(onSubmit)}
        // component={Link}
        //  component={Link}       
        onClick={handleSubmit(onSubmit)}
        // to="/user-management/users"
        type ="submit"
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif', // Add your desired font family here
            fontSize: '14px', // Customize font size
            // fontWeight: 'bold', // Optional: make it bold
            color: 'white', // Customize text color
          }}
        >
          Save
        </Typography>
      </IconButton>
      <IconButton
       sx={{ 
        backgroundColor: "primary.main", 
        width: 60, 
        height: 30, 
        borderRadius: '6px', 
        border: '1px solid grey',
        '&:hover': {
          backgroundColor: "primary.main" // Prevent hover effect
        },
        '&:focus': {
          backgroundColor: "primary.main" // Prevent focus effect
        }
      }}
        onClick={handleCancel}
        component={Link}
        to="/user-management/users"
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif', // Add your desired font family here
            fontSize: '14px', // Customize font size
            // fontWeight: 'bold', // Optional: make it bold
            color: 'white', // Customize text color
          }}
        >
          Cancel
        </Typography>
      </IconButton> */}

      <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
        <Button variant="outlined" size="small" component={Link} to="/user-management/users">
          Cancel
        </Button>
        <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
          Submit
        </Button>
      </Stack>
    </Box>
  );

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          {/* <Spinner /> */}
        </Box>
      ) : (
        <>
          {/* <CustomCardHeader title="Add Users" secondary={secondaryActions1} /> */}
          <CardActions
            sx={{
              position: 'sticky',
              top: 0, // ✅ Always stick at top
              bgcolor: 'background.default',
              zIndex: 1100, // ✅ Higher than tabs
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                Add Users
              </Typography>
              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                <Button variant="outlined" size="small" component={Link} to="/user-management/users">
                  Cancel
                </Button>
                <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
                  Submit
                </Button>
              </Stack>
            </Stack>
          </CardActions>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <MainCard sx={{ borderRadius: '0px', backgroundColor: 'transparent', border: 'none' }}>
                  {/* Username Field */}
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel>
                          First Name <span style={{ color: 'red' }}>*</span>
                        </InputLabel>
                        <CustomNameField
                          name="first_name"
                          control={control}
                          placeholder="Enter First Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel>
                          Last Name <span style={{ color: 'red' }}>*</span>
                        </InputLabel>
                        <CustomNameField
                          name="last_name"
                          control={control}
                          placeholder="Enter Last Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel>
                          Username <span style={{ color: 'red' }}>*</span>
                        </InputLabel>
                        <CustomUsernameField
                          name="username"
                          control={control}
                          placeholder="Enter User Name"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="email_id">
                          Client Email <span style={{ color: 'red' }}>*</span>
                        </InputLabel>
                        <CustomEmailField
                          name="email_id"
                          control={control}
                          placeholder="Enter Client Email"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel htmlFor="email_id">
                          Password <span style={{ color: 'red' }}>*</span>
                        </InputLabel>
                        <CustomPasswordField
                          name="password"
                          control={control}
                          placeholder="Enter your password"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                      <Stack spacing={1}>
                        <InputLabel>Role type</InputLabel>
                        <CustomDropdownFields
                          name="Client_name"
                          control={control}
                          placeholder="Select Client"
                          options={rolesOptions}
                          onChange={(e) => handleRoleChange(e.target.value)}
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid>

                    {/* Language Field */}
                  </Grid>
                </MainCard>
              </Grid>

              {roleType && (
                <>
                  <Grid item xs={12} lg={2} md={3} sm={4}>
                    <Grid Container spacing={3}>
                      <MainCard sx={{ borderRadius: '0px' }}>
                        {storedData.map((page, pageIndex) => {
                          // console.log(`Menu Index: ${data1}`); // Log the menu object and its index
                          console.log(`Page Index: ${pageIndex}`, page); // Log the page object and its index

                          return (
                            <List
                              key={pageIndex}
                              component="nav"
                              sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}
                            >
                              <ListItemButton
                                onClick={() => setSelectedMenu(page)}
                                sx={{
                                  backgroundColor: selectedMenu.name === page.name ? 'rgba(0, 0, 0, 0.08)' : 'transparent', // Fix hover for selected item
                                  '&:hover': {
                                    backgroundColor: 'rgba(0, 0, 0, 0.08)', // Background color on hover
                                    color: 'secondary.main', // Text color on hover
                                    borderRadius: '4px' // Optional: Add border-radius for hover effect
                                  }
                                }}
                              >
                                <ListItemIcon>
                                  {page.name === 'Left_Menu' && <MenuIcon />}
                                  {page.name === 'Top_Menu' && <ManageAccountsIcon />}
                                </ListItemIcon>
                                <ListItemText primary={page.name.replace('_', ' ')} />
                              </ListItemButton>
                            </List>
                          );
                        })}
                      </MainCard>
                    </Grid>
                  </Grid>

                  <Grid item xs={12} xl={10} lg={8} md={9} sm={8}>
                    <TableContainer component={Paper} sx={{ borderRadius: '0px' }}>
                      <Table>
                        <TableHead>
                          <TableRow sx={{ height: '20px', backgroundColor: 'white' }}>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontFamily: 'Roboto', color: 'rgb(117, 113, 113)' }}
                            >
                              Actions
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                            >
                              Read
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                            >
                              Write
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                            >
                              Update
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                            >
                              Delete
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                            >
                              Select All
                            </TableCell>
                          </TableRow>
                        </TableHead>

                        <TableBody>
                          {selectedMenu.children?.map((section, sectionIndex) => (
                            <React.Fragment key={sectionIndex}>
                              <TableRow sx={{ height: '30px' }}>
                                <TableCell
                                  colSpan={6}
                                  sx={{
                                    padding: '4px',
                                    backgroundColor: '#f5f5f5',
                                    fontWeight: 'bold',
                                    lineHeight: '1',
                                    textAlign: 'left'
                                  }}
                                >
                                  <Typography
                                    variant="h6"
                                    sx={{
                                      fontSize: '0.875rem',
                                      display: 'flex',
                                      alignItems: 'center',
                                      paddingLeft: '10px'
                                    }}
                                  >
                                    <span style={{ marginRight: '8px', marginTop: '3px' }}>
                                      {section.name === 'Jobs management' ? <WorkIcon fontSize="small" /> : <GroupIcon fontSize="small" />}
                                    </span>
                                    {section.name}
                                  </Typography>
                                </TableCell>
                              </TableRow>

                              {section?.children?.map((action, actionIndex) => {
                                console.log('Action Index:', actionIndex); // Logs the index of the current action
                                console.log('Action Object:', action); // Logs the action object being rendered
                                console.log('Section Object:', section); // Logs the action object being rendered
                                return (
                                  <TableRow key={actionIndex} sx={{ height: '32px' }}>
                                    <TableCell sx={{ padding: '10px', fontSize: '0.875rem', border: 0 }}>
                                      {action.name.replaceAll('_', ' ')}
                                    </TableCell>
                                    {Object.keys(permissionMapping).map((permissionKey) => (
                                      <TableCell align="center" key={permissionKey} sx={{ padding: '10px', border: 0 }}>
                                        <CustomCheckbox
                                          checked={isPermissionEnabled(
                                            updatedPermissions[action.name] || action.permissions,
                                            permissionKey
                                          )}
                                          onChange={() => handlePermissionToggle(action, permissionKey)} // Pass the full action object
                                        />
                                      </TableCell>
                                    ))}
                                  </TableRow>
                                );
                              })}
                            </React.Fragment>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                </>
              )}
            </Grid>
          </form>
          <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
            <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </>
      )}
    </>
  );
};

export default AddUsers;
