import { useLocation } from 'react-router-dom';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { useEffect, useState, useContext } from 'react';
import { Grid, TextField, MenuItem, Select, Stack, InputLabel } from '@mui/material';
// import { useNavigate } from "react-router-dom";
import MainCard from 'components/MainCard';
import List from '@mui/material/List';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemButton from '@mui/material/ListItemButton';
import MenuIcon from '@mui/icons-material/Menu';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import React from 'react';
import axios from 'axios';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Box, IconButton } from '@mui/material';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import { Link } from 'react-router-dom';
import GroupIcon from '@mui/icons-material/Group';
import CustomCheckbox from '../roles/checkbox';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
import { useNavigate } from 'react-router-dom';
import JWTContext from 'contexts/JWTContext';
import Button from '@mui/material/Button';
// import Checkbox from "@mui/material";
import { Tab, Checkbox } from '@mui/material';
import CardActions from '@mui/material/CardActions';
import { HEADER_HEIGHT } from 'config';
import { useTheme } from '@mui/material/styles';

import Spinner from 'components/custom-components/Spinner';

import CustomUsernameField from 'components/custom-components/CustomUsernameField';

const API_URL = import.meta.env.VITE_APP_API_URL;
const EditUsersPage = () => {
  console.log('hitting me edit users ');
  const navigate = useNavigate();
  const [previousPermissions, setPreviousPermissions] = useState({});
  const [menuStructure, setMenuStructure] = useState([]);
  const [updatedPermissions, setUpdatedPermissions] = useState({});
  const [selectedMenu, setSelectedMenu] = useState(null);
  const theme = useTheme();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const roleData = location.state?.row?.original; // Access role data
  console.log('this is role data', roleData?.role?.permissions);
  const [parentRole, setParentRole] = useState([]);

  const [selectedParentRole, setSelectedParentRole] = useState(' ');
  console.log('this is seleeted rol', selectedParentRole);
  useEffect(() => {
    if (roleData?.role?.permissions && roleData.role?.permissions.length > 0) {
      setSelectedMenu(roleData.role?.permissions[0]);
      // setPermissions(roleData.role?.permissions[0]);
    }
  }, [roleData]);

  const { user } = useContext(JWTContext);

  console.log('this is state ***********************', user);

  const permissionMapping = {
    Read: 1, // Binary 0001
    Write: 2, // Binary 0010
    Update: 4, // Binary 0100
    Delete: 8, // Binary 1000
    Select_All: 15 // Full permissions
  };

  const isPermissionEnabled = (permissions, permissionKey) => {
    if (!permissions) return false; // Ensure undefined or 0 means unchecked
    const bitValue = permissionMapping[permissionKey];
    return (permissions & bitValue) === bitValue;
  };

  const handlePermissionToggle = (action, permissionKey) => {
    console.log('🟡 DEBUG: Action object received ->', action);
    console.log('🟡 DEBUG: Permission key ->', permissionKey);
    setUpdatedPermissions((prev) => {
      console.log('🟢 PREVIOUS STATE:', prev);
      const currentPermissions = prev[action.name] ?? action.permissions;
      console.log('🔵 Current permissions for', action.name, ':', currentPermissions);

      const bitValue = permissionMapping[permissionKey];

      let newPermissions;

      if (permissionKey === 'Select_All') {
        if ((currentPermissions & bitValue) === bitValue) {
          // ✅ If "Select_All" is checked, we save the current permissions
          setPreviousPermissions((prevPermissions) => ({
            ...prevPermissions,
            [action.name]: currentPermissions
          }));

          // ✅ Force update state to reset all permissions to 0
          newPermissions = 0;
        } else {
          // ✅ If "Select_All" is checked again, restore previous permissions OR full permissions
          newPermissions = previousPermissions[action.role_name] || permissionMapping['Select_All'];
        }
      } else {
        // ✅ Toggle specific permissions (Read, Write, Update, Delete)
        newPermissions =
          (currentPermissions & bitValue) === bitValue
            ? currentPermissions & ~bitValue // Remove the sspecific permission
            : currentPermissions | bitValue; // Add the specific permission
      }

      console.log('🔵 New permissions for', action.name, ':', newPermissions);
      return {
        ...prev,
        [action.name]: newPermissions // ✅ Ensures state updates correctly
      };
    });
  };

  const { control, handleSubmit, setValue } = useForm({
    // defaultValues: {
    //   username: "",
    //   roleType: "",
    //   description: "",
    //   menuStructure: [],
    // },
  });

  useEffect(() => {
    setLoading(false); // Disable loading after initial mount
  }, []);
  const onSubmit = async (data) => {
    console.log('submitted data', data);
    const updatedMenuStructure = roleData?.role?.permissions.map((menu) => ({
      ...menu,
      children: (menu.children || []).map((section) => {
        console.log(`🔍 Updating section: ${section.name}`);
        console.log('🔄 Updated permission value:', updatedPermissions[section.name]);

        return {
          ...section,
          permissions: updatedPermissions.hasOwnProperty(section.name) // ✅ Ensure section permissions are updated
            ? updatedPermissions[section.name]
            : section.permissions, // ✅ Fallback to existing permissions
          children: (section.children || []).map((action) => {
            console.log(`🔍 Updating action: ${action.name} under section: ${section.name}`);
            console.log('🔄 Updated action permission value:', updatedPermissions[action.name]);

            return {
              ...action,
              permissions: updatedPermissions.hasOwnProperty(action.name) // ✅ Ensure action permissions are updated
                ? updatedPermissions[action.name]
                : action.permissions // ✅ Fallback to existing permissions
            };
          })
        };
      })
    }));

    const finalRoleId = selectedParentRole && selectedParentRole !== ' ' ? selectedParentRole : roleData.role_id;

    const finalData = {
      username: data.user_name,
      first_name: data.first_name,
      last_name: data.last_name,
      role_id: finalRoleId, // ✅ Send updated permissions
      permissions: updatedMenuStructure
    };

    console.log('updated menu struture', finalData);

    try {
      const response = await axios.put(
        `${API_URL}/individualsRoles/update-user/${roleData.id}`,
        {
          username: data.user_name,
          first_name: data.first_name,
          last_name: data.last_name,
          role_id: finalRoleId, // ✅ Send updated permissions
          permissions: updatedMenuStructure
        },
        {
          headers: { Authorization: `Bearer ${localStorage.getItem('serviceToken')}` }
        }
      );

      console.log('Updated Role:', response.data);
      navigate('/user-management/users'); // Redirect after success
    } catch (error) {
      console.error('Error updating role:', error);
    }
  };

  useEffect(() => {
    if (menuStructure?.length > 0) {
      setSelectedMenu(menuStructure[0]); // Select the first menu by default
    }
  }, [menuStructure]);

  useEffect(() => {
    const fetchParentLevels = async () => {
      const token = localStorage.getItem('serviceToken');

      if (!token) {
        console.error('❌ No token found in localStorage. User may not be logged in.');
        return;
      }

      try {
        const res = await axios.get(`${API_URL}/roles/level1`, {
          headers: {
            Authorization: `Bearer ${token}` // ✅ Attach token to request
          }
        });

        console.log('✅ Response Data:', res.data);
        setLoading(false);
        // console.log("🔍 User Level:", userLevel);

        // ✅ Ensure userAllowedLevels is correctly defined
        // const userAllowedLevels = res.data.find((l) => l.level === userLevel)?.can_create || [];
        // console.log("🚀 User Allowed Levels:", userAllowedLevels);

        // ✅ Ensure levels is correctly filtered
        setParentRole(res.data);
      } catch (error) {
        console.error('❌ Error fetching levels:', error);

        if (error.response) {
          console.error('🚨 Response Status:', error.response.status);
          console.error('🚨 Response Data:', error.response.data);
          setLoading(false);

          if (error.response.status === 401) {
            console.error('❌ Unauthorized: Invalid or expired token.');
            alert('Session expired. Please log in again.');
            // Optionally redirect to login page:
            // window.location.href = "/login";
          }
        }
      }
    };
    fetchParentLevels();
  }, []);

  // const secondaryActions1 = (
  //   <Box sx={{ display: 'flex', gap: 1 }}>
  //     <IconButton
  //       sx={{
  //         backgroundColor: "primary.main",
  //         width: 60,
  //         height: 30,
  //         borderRadius: '6px',
  //         border: '1px solid grey',
  //         '&:hover': {
  //           backgroundColor: "primary.main" // Prevent hover effect
  //         },
  //         '&:focus': {
  //           backgroundColor: "primary.main" // Prevent focus effect
  //         }
  //       }}
  //       // onClick={handleSubmit(onSubmit)}
  //       // component={Link}
  //       // to="/user-management/users"
  //        type="submit"
  //     >
  //       <Typography
  //         sx={{
  //           fontFamily: 'Arial, sans-serif', // Add your desired font family here
  //           fontSize: '14px', // Customize font size
  //           // fontWeight: 'bold', // Optional: make it bold
  //           color: 'white', // Customize text color
  //         }}
  //       >
  //         Save
  //       </Typography>
  //       {/* <Button >
  //           Save
  //       </Button> */}
  //     </IconButton>
  //     <IconButton
  //       sx={{
  //         backgroundColor: "primary.main",
  //         width: 60,
  //         height: 30,
  //         borderRadius: '6px',
  //         border: '1px solid grey',
  //         '&:hover': {
  //           backgroundColor: "primary.main" // Prevent hover effect
  //         },
  //         '&:focus': {
  //           backgroundColor: "primary.main" // Prevent focus effect
  //         }
  //       }}
  //       // onClick={handleCancel}
  //       component={Link}
  //       to="/user-management/users"
  //     >
  //       <Typography
  //         sx={{
  //           fontFamily: 'Arial, sans-serif', // Add your desired font family here
  //           fontSize: '14px', // Customize font size
  //           // fontWeight: 'bold', // Optional: make it bold
  //           color: 'white', // Customize text color
  //         }}
  //       >
  //         Cancel
  //       </Typography>

  //     </IconButton>
  //   </Box>
  // );

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
        <Button variant="outlined" size="small" component={Link} to="/user-management/users">
          Cancel
        </Button>
        <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
          Submit
        </Button>
      </Stack>
    </Box>
  );

  const [roles, setRoles] = useState([]);
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        console.log('It is fetcing ');
        const response = await axios.get(`${API_URL}/roles`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('serviceToken')}`
          }
        });
        console.log('this is repsonse', response.data);
        if (Array.isArray(response.data)) {
          setRoles(response.data);
        } else {
          console.error('Unexpected data format:', response.data);
        }
      } catch (error) {
        console.error('Failed to fetch clients:', error);
      }
    };
    fetchRoles();
  }, []);

  const handleRoleChange = (selectedValue) => {
    console.log('this is selected value ', selectedValue);
    const selectedRoles = roles.find((role) => role.id === selectedValue);
    setSelectedParentRole(selectedRoles.id);

    console.log('this is seleeted client ', selectedRoles.id);
  };

  const rolesOptions = roles.map((role) => ({
    value: role.id,
    label: role.role_name
  }));

  // console.log("User object:", user);
  // console.log("User roles:", user?.roles);

  const loggedInUserLevel = user?.role?.level_name;
  console.log('this to check the dteials**********', loggedInUserLevel);

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          {/* <Spinner /> */}
        </Box>
      ) : (
        <>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* <CustomCardHeader title=" Edit Users" secondary={secondaryActions1} /> */}
            <CardActions
              sx={{
                position: 'sticky',
                top: 0, // ✅ Always stick at top
                bgcolor: 'background.default',
                zIndex: 1100, // ✅ Higher than tabs
                borderBottom: '1px solid',
                borderBottomColor: theme.palette.divider,
                padding: '8px 16px'
              }}
            >
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
                <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                  Edit Users
                </Typography>
                <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                  <Button variant="outlined" size="small" component={Link} to="/user-management/users">
                    Cancel
                  </Button>
                  <Button variant="contained" size="small" onClick={handleSubmit(onSubmit)} type="submit">
                    Submit
                  </Button>
                </Stack>
              </Stack>
            </CardActions>
            <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <MainCard sx={{ borderRadius: '0px', backgroundColor: 'transparent', border: 'none' }}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        <Stack spacing={1}>
                          <InputLabel>
                            First Name <span style={{ color: 'red' }}>*</span>
                          </InputLabel>
                          <CustomNameField
                            name="first_name"
                            control={control}
                            placeholder="Enter First Name"
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            defaultValue={roleData.user.first_name || ''}
                          />
                        </Stack>
                      </Grid>

                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        <Stack spacing={1}>
                          <InputLabel>
                            Last Name <span style={{ color: 'red' }}>*</span>
                          </InputLabel>
                          <CustomNameField
                            name="last_name"
                            control={control}
                            placeholder="Enter Last Name"
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            defaultValue={roleData.user.last_name || ''}
                          />
                        </Stack>
                      </Grid>

                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        {/* <Stack spacing={1}>
                    <InputLabel htmlFor="email_id">Client Email</InputLabel>
                    <CustomEmailField
                      name="email_id"
                      control={control}
                      placeholder="Enter Email"
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      defaultValue={roleData.user.email || ""}
                      disabled={true}
                    />
                  </Stack> */}
                        <Stack spacing={1}>
                          <Typography color="secondary">Email</Typography>
                          <Typography>{roleData.user.email || ''}</Typography>
                        </Stack>
                      </Grid>

                      <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                        <Stack spacing={1}>
                          <InputLabel>
                            Username <span style={{ color: 'red' }}>*</span>
                          </InputLabel>
                          <CustomUsernameField
                            name="user_name"
                            control={control}
                            placeholder="Enter User Name"
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                            defaultValue={roleData.user.username || ''}
                          />
                        </Stack>
                      </Grid>

                      {loggedInUserLevel === 'Level 0' &&
                        (roleData.role.level_name === 'Level 2' || roleData.role.level_name === 'Level 3') && (
                          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
                            <Stack spacing={1}>
                              <InputLabel>Role type</InputLabel>
                              <CustomDropdownFields
                                name="role_id"
                                control={control}
                                placeholder="Select Client"
                                options={rolesOptions}
                                onChange={(e) => handleRoleChange(e.target.value)}
                                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                              />
                            </Stack>
                          </Grid>
                        )}
                    </Grid>
                  </MainCard>
                </Grid>

                <Grid item xs={12} lg={2} md={3} sm={4}>
                  <Grid Container spacing={3}>
                    <MainCard sx={{ borderRadius: '0px' }}>
                      {roleData?.role?.permissions?.map((page, pageIndex) => {
                        // console.log(`Menu Index: ${data1}`); // Log the menu object and its index
                        console.log(`Page Index: ${pageIndex}`, page); // Log the page object and its index

                        return (
                          <List
                            key={pageIndex}
                            component="nav"
                            sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'secondary.main' } }}
                          >
                            <ListItemButton
                              onClick={() => setSelectedMenu(page)}
                              sx={{
                                backgroundColor: selectedMenu?.name === page.name ? 'rgba(0, 0, 0, 0.08)' : 'transparent', // Fix hover for selected item
                                '&:hover': {
                                  backgroundColor: 'rgba(0, 0, 0, 0.08)', // Background color on hover
                                  color: 'secondary.main', // Text color on hover
                                  borderRadius: '4px' // Optional: Add border-radius for hover effect
                                }
                              }}
                            >
                              <ListItemIcon>
                                {page.name === 'Left_Menu' && <MenuIcon />}
                                {page.name === 'Top_Menu' && <ManageAccountsIcon />}
                              </ListItemIcon>
                              <ListItemText primary={page.name.replace('_', ' ')} />
                            </ListItemButton>
                          </List>
                        );
                      })}
                    </MainCard>
                  </Grid>
                </Grid>

                {/* <Grid item xs={12} xl ={10} lg={8} md={9} sm={8}>
            <TableContainer component={Paper} sx={{ borderRadius: "0px" }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ height: "20px", backgroundColor: "white" }}>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem" ,fontFamily: "Roboto", color: "rgb(117, 113, 113)",  }}>
                      Actions
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Read
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold" , color: "rgb(117, 113, 113)"}}>
                      Write
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Update
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Delete
                    </TableCell>
                    <TableCell align="center" sx={{ padding: "4px", fontSize: "0.875rem", fontWeight: "bold", color: "rgb(117, 113, 113)" }}>
                      Select All
                    </TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {selectedMenu?.children?.map((section, sectionIndex) => (
                    <React.Fragment key={sectionIndex}>
                    
                      <TableRow sx={{ height: "30px" }}>
                        <TableCell
                          colSpan={6}
                          sx={{
                            padding: "4px",
                            backgroundColor: "#f5f5f5",
                            fontWeight: "bold",
                            lineHeight: "1",
                            textAlign: "left",
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontSize: "0.875rem",
                              display: "flex",
                              alignItems: "center",
                              paddingLeft: "10px",
                            }}
                          >
                            <span style={{ marginRight: "8px", marginTop: "3px" }}>
                              {section.name === "Jobs management" ? (
                                <WorkIcon fontSize="small" />
                              ) : (
                                <GroupIcon fontSize="small" />
                              )}
                            </span>
                            {section.name}
                          </Typography>
                        </TableCell>
                      </TableRow>
                  
                      {section?.children?.map((action, actionIndex) => {
                          console.log("Action Index:", actionIndex); // Logs the index of the current action
                          console.log("Action Object:", action); // Logs the action object being rendered
                          console.log("Section Object:", section); // Logs the action object being rendered
                          return (
                            <TableRow key={actionIndex} sx={{ height: "32px" }}>
                              <TableCell sx={{ padding: "10px", fontSize: "0.875rem", border: 0 }}>
                                {action.name.replaceAll("_", " ")}
                              </TableCell>
                              {Object.keys(permissionMapping).map((permissionKey) => (
                                <TableCell
                                  align="center"
                                  key={permissionKey}
                                  sx={{ padding: "10px", border: 0 }}
                                >
                              
                              <CustomCheckbox
                                    checked={isPermissionEnabled(updatedPermissions[action.name] ?? action.permissions, permissionKey)}
                                    onChange={() => handlePermissionToggle(action, permissionKey)}
                                  />
                                     
                                  
                                </TableCell>
                              ))}
                            </TableRow>
                          );
                        })}

                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

        </Grid> */}

                <Grid item xs={12} xl={10} lg={8} md={9} sm={8}>
                  <TableContainer component={Paper} sx={{ borderRadius: '0px' }}>
                    <Table>
                      <TableHead>
                        <TableRow sx={{ height: '20px', backgroundColor: 'white' }}>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontFamily: 'Roboto', color: 'rgb(117, 113, 113)' }}
                          >
                            Actions
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                          >
                            Read
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                          >
                            Write
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                          >
                            Update
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                          >
                            Delete
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ padding: '4px', fontSize: '0.875rem', fontWeight: 'bold', color: 'rgb(117, 113, 113)' }}
                          >
                            Select All
                          </TableCell>
                        </TableRow>
                      </TableHead>

                      {/* <TableBody>
                                  {selectedMenu?.children?.map((section, sectionIndex) => (
                                    <React.Fragment key={sectionIndex}>
                                      <TableRow sx={{ height: '30px' }}>
                                        <TableCell
                                          colSpan={6}
                                          sx={{
                                            padding: '4px',
                                            backgroundColor: '#f5f5f5',
                                            fontWeight: 'bold',
                                            lineHeight: '1',
                                            textAlign: 'left'
                                          }}
                                        >
                                          <Typography
                                            variant="h6"
                                            sx={{
                                              fontSize: '0.875rem',
                                              display: 'flex',
                                              alignItems: 'center',
                                              paddingLeft: '10px'
                                            }}
                                          >
                                            {section.name}
                                          </Typography>
                                        </TableCell>
            
                                        {Object.keys(permissionMapping).map((permissionKey) => (
                                          <TableCell align="center" key={permissionKey} sx={{ padding: '10px', border: 0 }}>
                                            <Checkbox
                                              checked={isPermissionEnabled(updatedPermissions[section.name] ?? section.permissions, permissionKey)}
                                              onChange={() => handlePermissionToggle(section, permissionKey)}
                                            />
                                          </TableCell>
                                        ))}
                                      </TableRow>
                                    </React.Fragment>
                                  ))}
                                </TableBody> */}
                      <TableBody>
                        {selectedMenu?.children?.map((section, sectionIndex) => {
                          return (
                            <TableRow key={sectionIndex} sx={{ height: '30px', backgroundColor: '#f5f5f5' }}>
                              {/* Section Name */}
                              <TableCell sx={{ padding: '4px', fontWeight: 'bold', lineHeight: '1', textAlign: 'left' }}>
                                <Typography variant="h6" sx={{ fontSize: '0.875rem', paddingLeft: '10px' }}>
                                  {section.name}
                                </Typography>
                              </TableCell>

                              {/* Permissions Checkboxes */}
                              {Object.keys(permissionMapping).map((permissionKey) => (
                                <TableCell align="center" key={permissionKey} sx={{ padding: '10px', border: 0 }}>
                                  <Checkbox
                                    checked={isPermissionEnabled(updatedPermissions[section.name] ?? section.permissions, permissionKey)}
                                    onChange={() => handlePermissionToggle(section, permissionKey)}
                                  />
                                </TableCell>
                              ))}

                              {/* Select All Checkbox */}
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </MainCard>
          </form>
        </>
      )}
    </>
  );
};

export default EditUsersPage;
