import React from "react";
import PropTypes from "prop-types";
import { Typography, Tooltip, Chip } from "@mui/material";

// ✅ Helper to get status info
const getStatusInfo = (isActive) => {
  const statusMap = {
    true: { label: "Active", color: "success" },
    false: { label: "Inactive", color: "error" },
  };
  return statusMap[isActive] || { label: "Unknown", color: "warning" };
};

// ✅ Reusable tooltip + typography renderer
const renderWithTooltip = (value, options = {}) => (
  <Tooltip
    title={value}
    placement={options.placement || "bottom"}
    arrow={options.arrow || false}
  >
    <Typography
      variant="body2"
      component={options.component || "span"}
      sx={{
        display: "inline-block",
        ...(options.sx || {}),
      }}
      noWrap
    >
      {value}
    </Typography>
  </Tooltip>
);

// ------------------- Cells -------------------

export const RoleNameCell = ({ value }) =>
  renderWithTooltip(value, { placement: "bottom" });
RoleNameCell.propTypes = { value: PropTypes.string.isRequired };

export const LevelNameCell = ({ value }) =>
  renderWithTooltip(value, { placement: "top" });
LevelNameCell.propTypes = { value: PropTypes.string.isRequired };

export const DescriptionCell = ({ value }) =>
  renderWithTooltip(value, {
    placement: "bottom-start",
    sx: { fontStyle: "italic" },
  });
DescriptionCell.propTypes = { value: PropTypes.string.isRequired };

export const ParentRoleCell = ({ value }) =>
  renderWithTooltip(value, {
    arrow: true,
    sx: { display: "inline-flex" },
  });
ParentRoleCell.propTypes = { value: PropTypes.string.isRequired };

// ✅ Status cell stays custom with Chip
export const StatusCell = ({ value }) => {
  const { label, color } = getStatusInfo(value);
  return (
    <Tooltip title={label} placement="bottom" arrow>
      <Chip color={color} label={label} size="small" variant="outlined" />
    </Tooltip>
  );
};
StatusCell.propTypes = { value: PropTypes.bool.isRequired };
