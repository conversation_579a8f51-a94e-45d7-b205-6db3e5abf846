import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export const useEntityForm = ({ initialSiteMap, tenantsList, user, editData }) => {
  const { setValue, control, handleSubmit, formState } = useForm();
  const [initialSiteMapData, setInitialSiteMapData] = useState(initialSiteMap);
  const [tenants, setTenants] = useState(tenantsList);
  const [tenantId, setTenantId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (editData) {
      setValue('name', editData?.name || '');
      setValue('description', editData?.description || '');
      setTenantId(editData?.orgId || '');
      setInitialSiteMapData(editData?.permissions || initialSiteMap);
    }
  }, [editData, setValue, initialSiteMap]);

  useEffect(() => {
    if (user?.organisationCategory === 'Chidhagni') {
      setTenants(tenantsList);
    } else {
      setTenants([{ value: user?.orgId || 'default', key: user?.orgName || 'Default Organization' }]);
      setTenantId(user?.orgId || 'default');
    }
  }, [user, tenantsList]);

  return {
    setValue,
    control,
    handleSubmit,
    formState,
    initialSiteMapData,
    setInitialSiteMapData,
    tenants,
    tenantId,
    setTenantId,
    isSubmitting,
    setIsSubmitting
  };
};


