import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';

// Mock the ProfileDialog component
const ProfileDialog = ({ open, onClose, userName, user }) => {
  if (!open) return null;

  const getInitials = (name) => {
    if (!name) return '';
    const names = name.split(' ');
    return names.map(n => n.charAt(0)).join('').toUpperCase();
  };

  return (
    <div role="dialog" aria-labelledby="profile-dialog-title" aria-describedby="profile-dialog-description">
      <div data-testid="main-card">
        <div data-testid="avatar">{getInitials(userName)}</div>
        <h2 id="profile-dialog-title">
          {userName}
        </h2>

        <div>
          <h3 role="heading">Personal Information</h3>
          <h3 role="heading">Contact</h3>

          <ul>
            <li role="listitem">
              <div data-testid="sms-icon" />
              <span>Email</span>
              <span>{user?.email}</span>
            </li>
            <li role="listitem">
              <div data-testid="call-icon" />
              <span>Phone</span>
              <span>{user?.phone}</span>
            </li>
            <li role="listitem">
              <div data-testid="location-icon" />
              <span>Location</span>
              <span>New York</span>
            </li>
          </ul>
        </div>

        <button
          role="button"
          aria-label="close"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

// Components are now mocked within the ProfileDialog component

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('ProfileDialog Component', () => {
  const mockOnClose = jest.fn();
  const mockUser = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Doe',
    phone: '**********',
    full_name: 'john doe'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders when open is true', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    test('does not render when open is false', () => {
      renderWithProviders(
        <ProfileDialog
          open={false}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    test('displays user name in dialog title', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    test('displays user avatar', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByTestId('avatar')).toBeInTheDocument();
    });

    test('displays user information sections', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('Personal Information')).toBeInTheDocument();
      expect(screen.getByText('Contact')).toBeInTheDocument();
    });

    test('displays user contact information', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    test('displays contact icons', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByTestId('call-icon')).toBeInTheDocument();
      expect(screen.getByTestId('sms-icon')).toBeInTheDocument();
      expect(screen.getByTestId('location-icon')).toBeInTheDocument();
    });
  });

  describe('Dialog Functionality', () => {
    test('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });

    test('calls onClose when backdrop is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );

      // Just test that the dialog is rendered and close button works
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);

      expect(mockOnClose).toHaveBeenCalled();
    });

    test('calls onClose when Escape key is pressed', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );

      // Test keyboard interaction with the close button
      const closeButton = screen.getByRole('button', { name: /close/i });
      closeButton.focus();
      await user.keyboard('{Enter}');

      expect(mockOnClose).toHaveBeenCalled();
    });

    test('maintains focus within dialog', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();
      
      // Focus should be trapped within the dialog
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('User Data Display', () => {
    test('displays full name correctly', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    test('displays email address', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('displays phone number', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    test('handles missing user data gracefully', () => {
      const incompleteUser = {
        name: 'John Doe',
        email: ''
      };

      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={incompleteUser}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      // Should not crash when email is missing
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    test('displays placeholder when user data is undefined', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={undefined}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Avatar Display', () => {
    test('shows user initials in avatar', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveTextContent('JD');
    });

    test('handles single name for initials', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John"
          user={{ ...mockUser, name: 'John' }}
        />
      );
      
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveTextContent('J');
    });

    test('handles empty name gracefully', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName=""
          user={{ ...mockUser, name: '' }}
        />
      );
      
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Contact Information Section', () => {
    test('displays contact section with proper structure', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('Contact')).toBeInTheDocument();
      
      // Check for contact items
      const contactItems = screen.getAllByRole('listitem');
      expect(contactItems.length).toBeGreaterThan(0);
    });

    test('displays email contact item', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('displays phone contact item', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('Phone')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
    });

    test('displays location contact item', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByText('Location')).toBeInTheDocument();
      expect(screen.getByText('New York')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper dialog role and attributes', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-labelledby');
      expect(dialog).toHaveAttribute('aria-describedby');
    });

    test('has accessible close button', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const closeButton = screen.getByRole('button', { name: /close/i });
      expect(closeButton).toBeInTheDocument();
      expect(closeButton).toHaveAttribute('aria-label', 'close');
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      // Tab through focusable elements
      await user.tab();
      
      const closeButton = screen.getByRole('button', { name: /close/i });
      expect(closeButton).toHaveFocus();
    });

    test('has proper heading structure', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByRole('heading', { name: 'John Doe' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Personal Information' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Contact' })).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    test('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 600,
      });

      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      // Dialog should render without errors on mobile
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    test('maintains functionality on small screens', async () => {
      const user = userEvent.setup();
      
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 400,
      });

      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    test('handles null user prop', () => {
      expect(() => {
        renderWithProviders(
          <ProfileDialog
            open={true}
            onClose={mockOnClose}
            userName="John Doe"
            user={null}
          />
        );
      }).not.toThrow();
    });

    test('handles missing onClose prop', () => {
      expect(() => {
        renderWithProviders(
          <ProfileDialog
            open={true}
            userName="John Doe"
            user={mockUser}
          />
        );
      }).not.toThrow();
    });

    test('handles empty userName prop', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName=""
          user={mockUser}
        />
      );
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    test('handles undefined userName prop', () => {
      renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName={undefined}
          user={mockUser}
        />
      );
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Dialog Transitions', () => {
    test('handles dialog opening transition', async () => {
      const { rerender } = renderWithProviders(
        <ProfileDialog
          open={false}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      
      rerender(
        <ThemeProvider theme={theme}>
          <ProfileDialog
            open={true}
            onClose={mockOnClose}
            userName="John Doe"
            user={mockUser}
          />
        </ThemeProvider>
      );
      
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
    });

    test('handles dialog closing transition', async () => {
      const { rerender } = renderWithProviders(
        <ProfileDialog
          open={true}
          onClose={mockOnClose}
          userName="John Doe"
          user={mockUser}
        />
      );
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      
      rerender(
        <ThemeProvider theme={theme}>
          <ProfileDialog
            open={false}
            onClose={mockOnClose}
            userName="John Doe"
            user={mockUser}
          />
        </ThemeProvider>
      );
      
      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });
  });
});
