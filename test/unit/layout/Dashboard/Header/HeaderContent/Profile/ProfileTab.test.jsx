import React, { useState } from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';

// Mock the ProfileTab component
const ProfileTab = ({ handleLogout, onProfileClick }) => {
  const [value, setValue] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const handleListItemClick = (event, index) => {
    setSelectedIndex(index);
  };

  const user = {
    name: '<PERSON>',
    email: '<EMAIL>'
  };

  return (
    <div>
      <div data-testid="avatar">JD</div>
      <div>{user.name}</div>
      <div>{user.email}</div>

      {value === 0 && (
        <div>
          <button role="button" aria-label="View Profile" onClick={onProfileClick}>
            <div data-testid="profile-icon" />
            View Profile
          </button>
          <button role="button" aria-label="Account Settings" onClick={() => setValue(1)}>
            <div data-testid="setting-icon" />
            Account Settings
          </button>
          <button role="button" aria-label="Logout" onClick={handleLogout}>
            <div data-testid="logout-icon" />
            Logout
          </button>
        </div>
      )}

      {value === 1 && (
        <div data-testid="setting-tab">
          <button
            data-testid="setting-item-0"
            onClick={(e) => handleListItemClick(e, 0)}
            className={selectedIndex === 0 ? 'selected' : ''}
          >
            Support
          </button>
          <button
            data-testid="setting-item-1"
            onClick={(e) => handleListItemClick(e, 1)}
            className={selectedIndex === 1 ? 'selected' : ''}
          >
            Account Settings
          </button>
          <button
            data-testid="setting-item-2"
            onClick={(e) => handleListItemClick(e, 2)}
            className={selectedIndex === 2 ? 'selected' : ''}
          >
            Privacy Center
          </button>
          <button
            data-testid="setting-item-3"
            onClick={(e) => handleListItemClick(e, 3)}
            className={selectedIndex === 3 ? 'selected' : ''}
          >
            Feedback
          </button>
          <button
            data-testid="setting-item-4"
            onClick={(e) => handleListItemClick(e, 4)}
            className={selectedIndex === 4 ? 'selected' : ''}
          >
            History
          </button>
        </div>
      )}
    </div>
  );
};

// Components are now mocked within the ProfileTab component

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('ProfileTab Component', () => {
  const mockHandleLogout = jest.fn();
  const mockOnProfileClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders without crashing', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      expect(screen.getByTestId('avatar')).toBeInTheDocument();
    });

    test('displays user information', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('renders profile menu items', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      expect(screen.getByText('View Profile')).toBeInTheDocument();
      expect(screen.getByText('Account Settings')).toBeInTheDocument();
      expect(screen.getByText('Logout')).toBeInTheDocument();
    });

    test('displays correct icons for menu items', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      expect(screen.getByTestId('profile-icon')).toBeInTheDocument();
      expect(screen.getByTestId('setting-icon')).toBeInTheDocument();
      expect(screen.getByTestId('logout-icon')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    test('calls onProfileClick when View Profile is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      await user.click(profileButton);
      
      expect(mockOnProfileClick).toHaveBeenCalled();
    });

    test('calls handleLogout when Logout is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      await user.click(logoutButton);
      
      expect(mockHandleLogout).toHaveBeenCalled();
    });

    test('opens settings tab when Account Settings is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('setting-tab')).toBeInTheDocument();
      });
    });

    test('shows settings tab with correct items', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByText('Support')).toBeInTheDocument();
        expect(screen.getByText('Account Settings')).toBeInTheDocument();
        expect(screen.getByText('Privacy Center')).toBeInTheDocument();
        expect(screen.getByText('Feedback')).toBeInTheDocument();
        expect(screen.getByText('History')).toBeInTheDocument();
      });
    });

    test('handles settings item selection', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Open settings tab
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('setting-tab')).toBeInTheDocument();
      });
      
      // Click on Support item
      const supportItem = screen.getByTestId('setting-item-0');
      await user.click(supportItem);
      
      expect(supportItem).toHaveClass('selected');
    });

    test('switches between different settings items', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Open settings tab
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('setting-tab')).toBeInTheDocument();
      });
      
      // Click on Privacy Center item
      const privacyItem = screen.getByTestId('setting-item-2');
      await user.click(privacyItem);
      
      expect(privacyItem).toHaveClass('selected');
      
      // Click on Feedback item
      const feedbackItem = screen.getByTestId('setting-item-3');
      await user.click(feedbackItem);
      
      expect(feedbackItem).toHaveClass('selected');
      expect(privacyItem).not.toHaveClass('selected');
    });
  });

  describe('Tab Navigation', () => {
    test('switches between profile and settings tabs', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Initially should show profile tab
      expect(screen.getByText('View Profile')).toBeInTheDocument();
      expect(screen.queryByTestId('setting-tab')).not.toBeInTheDocument();
      
      // Click on Account Settings to switch to settings tab
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('setting-tab')).toBeInTheDocument();
      });
      
      // Profile items should not be visible
      expect(screen.queryByText('View Profile')).not.toBeInTheDocument();
    });

    test('maintains tab state correctly', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Switch to settings tab
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('setting-tab')).toBeInTheDocument();
      });
      
      // Select a settings item
      const historyItem = screen.getByTestId('setting-item-4');
      await user.click(historyItem);
      
      expect(historyItem).toHaveClass('selected');
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels for buttons', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      
      expect(profileButton).toBeInTheDocument();
      expect(logoutButton).toBeInTheDocument();
      expect(settingsButton).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Tab through the buttons
      await user.tab();
      expect(screen.getByRole('button', { name: /view profile/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /account settings/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /logout/i })).toHaveFocus();
    });

    test('supports Enter key for button activation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      profileButton.focus();
      
      await user.keyboard('{Enter}');
      
      expect(mockOnProfileClick).toHaveBeenCalled();
    });

    test('has proper tab structure for settings', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Open settings tab
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      await waitFor(() => {
        const settingItems = screen.getAllByRole('button');
        expect(settingItems.length).toBeGreaterThan(0);
      });
    });
  });

  describe('User Context Integration', () => {
    test('displays user data from JWT context', () => {
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('handles missing user data gracefully', () => {
      // Mock JWT context with minimal user data
      jest.doMock('contexts/JWTContext', () => {
        const mockReact = require('react');
        return {
          __esModule: true,
          default: mockReact.createContext({
            user: {
              name: '',
              email: ''
            }
          })
        };
      });

      expect(() => {
        renderWithProviders(
          <ProfileTab 
            handleLogout={mockHandleLogout}
            onProfileClick={mockOnProfileClick}
          />
        );
      }).not.toThrow();
    });

    test('displays fallback when user context is undefined', () => {
      // Mock JWT context with undefined user
      jest.doMock('contexts/JWTContext', () => {
        const mockReact = require('react');
        return {
          __esModule: true,
          default: mockReact.createContext({
            user: undefined
          })
        };
      });

      expect(() => {
        renderWithProviders(
          <ProfileTab 
            handleLogout={mockHandleLogout}
            onProfileClick={mockOnProfileClick}
          />
        );
      }).not.toThrow();
    });
  });

  describe('Event Handling', () => {
    test('prevents default behavior on menu item clicks', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      
      // Create a spy for preventDefault
      const preventDefaultSpy = jest.fn();
      const mockEvent = { preventDefault: preventDefaultSpy };
      
      fireEvent.click(profileButton, mockEvent);
      
      expect(mockOnProfileClick).toHaveBeenCalled();
    });

    test('handles rapid clicks gracefully', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      
      // Click multiple times rapidly
      await user.click(profileButton);
      await user.click(profileButton);
      await user.click(profileButton);
      
      expect(mockOnProfileClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('Responsive Design', () => {
    test('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 600,
      });

      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      // Component should render without errors on mobile
      expect(screen.getByTestId('avatar')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    test('maintains functionality on small screens', async () => {
      const user = userEvent.setup();
      
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 400,
      });

      renderWithProviders(
        <ProfileTab 
          handleLogout={mockHandleLogout}
          onProfileClick={mockOnProfileClick}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      await user.click(profileButton);
      
      expect(mockOnProfileClick).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    test('handles missing callback functions gracefully', () => {
      expect(() => {
        renderWithProviders(<ProfileTab />);
      }).not.toThrow();
    });

    test('handles undefined callback functions', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <ProfileTab 
          handleLogout={undefined}
          onProfileClick={undefined}
        />
      );
      
      const profileButton = screen.getByRole('button', { name: /view profile/i });
      
      // Should not throw error when clicking
      expect(() => user.click(profileButton)).not.toThrow();
    });
  });
});
