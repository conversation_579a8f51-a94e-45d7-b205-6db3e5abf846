import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';

// Mock the SettingTab component since we're testing its functionality
const SettingTab = ({ selectedIndex, handleListItemClick }) => {
  const menuItems = [
    { id: 0, text: 'Support', icon: 'heart' },
    { id: 1, text: 'Account Settings', icon: 'setting3' },
    { id: 2, text: 'Privacy Center', icon: 'profile' },
    { id: 3, text: 'Feedback', icon: 'dislike' },
    { id: 4, text: 'History', icon: 'clipboard' }
  ];

  return (
    <ul role="list">
      {menuItems.map((item) => (
        <li key={item.id} role="listitem">
          <button
            role="button"
            aria-selected={selectedIndex === item.id}
            onClick={(e) => handleListItemClick && handleListItemClick(e, item.id)}
          >
            <div data-testid={`${item.icon}-icon`} size="18" variant={item.icon === 'clipboard' ? 'Bulk' : undefined}>
              {item.icon}
            </div>
            {item.text}
          </button>
        </li>
      ))}
    </ul>
  );
};

// Icons are now mocked within the component

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('SettingTab Component', () => {
  const mockHandleListItemClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders without crashing', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      expect(screen.getByRole('list')).toBeInTheDocument();
    });

    test('renders all setting menu items', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      expect(screen.getByText('Support')).toBeInTheDocument();
      expect(screen.getByText('Account Settings')).toBeInTheDocument();
      expect(screen.getByText('Privacy Center')).toBeInTheDocument();
      expect(screen.getByText('Feedback')).toBeInTheDocument();
      expect(screen.getByText('History')).toBeInTheDocument();
    });

    test('displays correct icons for each menu item', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      expect(screen.getByTestId('heart-icon')).toBeInTheDocument(); // Support
      expect(screen.getByTestId('setting3-icon')).toBeInTheDocument(); // Account Settings
      expect(screen.getByTestId('profile-icon')).toBeInTheDocument(); // Privacy Center
      expect(screen.getByTestId('dislike-icon')).toBeInTheDocument(); // Feedback
      expect(screen.getByTestId('clipboard-icon')).toBeInTheDocument(); // History
    });

    test('renders menu items as buttons', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(5);
    });
  });

  describe('Selection State', () => {
    test('highlights selected item correctly', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      expect(supportButton).toHaveAttribute('aria-selected', 'true');
    });

    test('shows different selected item when selectedIndex changes', () => {
      const { rerender } = renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      let supportButton = screen.getByRole('button', { name: /support/i });
      let settingsButton = screen.getByRole('button', { name: /account settings/i });
      
      expect(supportButton).toHaveAttribute('aria-selected', 'true');
      expect(settingsButton).toHaveAttribute('aria-selected', 'false');
      
      // Change selected index
      rerender(
        <ThemeProvider theme={theme}>
          <SettingTab 
            selectedIndex={1}
            handleListItemClick={mockHandleListItemClick}
          />
        </ThemeProvider>
      );
      
      supportButton = screen.getByRole('button', { name: /support/i });
      settingsButton = screen.getByRole('button', { name: /account settings/i });
      
      expect(supportButton).toHaveAttribute('aria-selected', 'false');
      expect(settingsButton).toHaveAttribute('aria-selected', 'true');
    });

    test('handles no selection state', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={-1}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected', 'false');
      });
    });

    test('handles out of range selectedIndex', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={10}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected', 'false');
      });
    });
  });

  describe('User Interactions', () => {
    test('calls handleListItemClick when Support is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      await user.click(supportButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        0
      );
    });

    test('calls handleListItemClick when Account Settings is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const settingsButton = screen.getByRole('button', { name: /account settings/i });
      await user.click(settingsButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        1
      );
    });

    test('calls handleListItemClick when Privacy Center is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const privacyButton = screen.getByRole('button', { name: /privacy center/i });
      await user.click(privacyButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        2
      );
    });

    test('calls handleListItemClick when Feedback is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const feedbackButton = screen.getByRole('button', { name: /feedback/i });
      await user.click(feedbackButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        3
      );
    });

    test('calls handleListItemClick when History is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const historyButton = screen.getByRole('button', { name: /history/i });
      await user.click(historyButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        4
      );
    });

    test('handles rapid clicks gracefully', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      
      // Click multiple times rapidly
      await user.click(supportButton);
      await user.click(supportButton);
      await user.click(supportButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('Accessibility', () => {
    test('has proper list structure', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const list = screen.getByRole('list');
      expect(list).toBeInTheDocument();
      
      const listItems = screen.getAllByRole('listitem');
      expect(listItems).toHaveLength(5);
    });

    test('has proper button roles and attributes', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected');
      });
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      // Tab through the buttons
      await user.tab();
      expect(screen.getByRole('button', { name: /support/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /account settings/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /privacy center/i })).toHaveFocus();
    });

    test('supports Enter key for button activation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      supportButton.focus();
      
      await user.keyboard('{Enter}');
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        0
      );
    });

    test('supports Space key for button activation', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      supportButton.focus();
      
      await user.keyboard(' ');
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.any(Object),
        0
      );
    });

    test('has proper ARIA labels', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      expect(screen.getByRole('button', { name: /support/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /account settings/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /privacy center/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /feedback/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /history/i })).toBeInTheDocument();
    });
  });

  describe('Icon Display', () => {
    test('displays icons with correct size', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const heartIcon = screen.getByTestId('heart-icon');
      expect(heartIcon).toHaveAttribute('size', '18');
      
      const settingIcon = screen.getByTestId('setting3-icon');
      expect(settingIcon).toHaveAttribute('size', '18');
    });

    test('displays icons with correct variant', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const clipboardIcon = screen.getByTestId('clipboard-icon');
      expect(clipboardIcon).toHaveAttribute('variant', 'Bulk');
    });
  });

  describe('Event Handling', () => {
    test('passes correct event object to handleListItemClick', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      await user.click(supportButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'click'
        }),
        0
      );
    });

    test('handles missing handleListItemClick prop gracefully', async () => {
      const user = userEvent.setup();
      
      expect(() => {
        renderWithProviders(
          <SettingTab selectedIndex={0} />
        );
      }).not.toThrow();
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      
      // Should not throw error when clicking
      expect(() => user.click(supportButton)).not.toThrow();
    });

    test('handles undefined handleListItemClick prop', async () => {
      const user = userEvent.setup();
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={undefined}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      
      // Should not throw error when clicking
      expect(() => user.click(supportButton)).not.toThrow();
    });
  });

  describe('Responsive Design', () => {
    test('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 600,
      });

      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      // Component should render without errors on mobile
      expect(screen.getByRole('list')).toBeInTheDocument();
      expect(screen.getAllByRole('button')).toHaveLength(5);
    });

    test('maintains functionality on small screens', async () => {
      const user = userEvent.setup();
      
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 400,
      });

      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const supportButton = screen.getByRole('button', { name: /support/i });
      await user.click(supportButton);
      
      expect(mockHandleListItemClick).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    test('handles undefined selectedIndex', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={undefined}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected', 'false');
      });
    });

    test('handles null selectedIndex', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={null}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected', 'false');
      });
    });

    test('handles negative selectedIndex', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={-5}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAttribute('aria-selected', 'false');
      });
    });
  });

  describe('Component Structure', () => {
    test('maintains consistent list item structure', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const listItems = screen.getAllByRole('listitem');
      
      listItems.forEach(listItem => {
        // Each list item should contain a button
        expect(listItem.querySelector('button')).toBeInTheDocument();
        
        // Each button should have an icon and text
        const button = listItem.querySelector('button');
        expect(button.querySelector('[data-testid$="-icon"]')).toBeInTheDocument();
      });
    });

    test('has proper text content for each item', () => {
      renderWithProviders(
        <SettingTab 
          selectedIndex={0}
          handleListItemClick={mockHandleListItemClick}
        />
      );
      
      const expectedTexts = ['Support', 'Account Settings', 'Privacy Center', 'Feedback', 'History'];
      
      expectedTexts.forEach(text => {
        expect(screen.getByText(text)).toBeInTheDocument();
      });
    });
  });
});
