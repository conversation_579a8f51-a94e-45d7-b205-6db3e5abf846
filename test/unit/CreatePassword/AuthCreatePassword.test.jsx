import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('hooks/useScriptRef');
jest.mock('api/snackbar');
jest.mock('utils/password-strength');
jest.mock('axios');

jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext()
  };
});

// Mock utils/axios to prevent import.meta issues
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: {
      common: {}
    }
  }
}));



// Mock the components
jest.mock('components/@extended/IconButton', () => {
  return function MockIconButton({ children, onClick, ...props }) {
    return (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div>{children}</div>;
  };
});

// Mock the icons
jest.mock('iconsax-react', () => ({
  Eye: () => <span data-testid="eye-icon">Eye</span>,
  EyeSlash: () => <span data-testid="eye-slash-icon">EyeSlash</span>
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock axios
const mockAxios = require('axios');

// Mock environment variables
process.env.VITE_APP_API_URL = 'http://localhost:3000';

// Now import the component after mocking dependencies
import AuthCreatePassword from '../../../src/sections/auth/auth-forms/AuthCreatePassword';

describe('AuthCreatePassword Component - Comprehensive Tests', () => {
  let mockScriptRef;
  let mockOpenSnackbar;
  let mockStrengthIndicator;
  let mockStrengthColor;
  let useScriptRef;
  let openSnackbar;
  let strengthIndicator;
  let strengthColor;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock functions
    mockScriptRef = { current: true };
    mockOpenSnackbar = jest.fn();
    mockStrengthIndicator = jest.fn().mockReturnValue(1);
    mockStrengthColor = jest.fn().mockReturnValue({ color: '#ff0000', label: 'Weak' });

    // Mock the hooks and utilities
    useScriptRef = require('hooks/useScriptRef').default;
    openSnackbar = require('api/snackbar').openSnackbar;
    strengthIndicator = require('utils/password-strength').strengthIndicator;
    strengthColor = require('utils/password-strength').strengthColor;

    useScriptRef.mockReturnValue(mockScriptRef);
    openSnackbar.mockImplementation(mockOpenSnackbar);
    strengthIndicator.mockImplementation(mockStrengthIndicator);
    strengthColor.mockImplementation(mockStrengthColor);

    // Mock axios
    mockAxios.post.mockResolvedValue({ data: {} });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders create password form with all required fields', () => {
      render(<AuthCreatePassword />);

      // Check for password input
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^confirm password$/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /create password/i })).toBeInTheDocument();

      // Check for password visibility toggle buttons (both have same aria-label)
      expect(screen.getAllByRole('button', { name: /toggle password visibility/i })).toHaveLength(2);
    });

    test('renders input fields with correct attributes', () => {
      render(<AuthCreatePassword />);

      // Check password input
      const passwordInput = screen.getByLabelText(/^password$/i);
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(passwordInput).toHaveAttribute('placeholder', 'Enter password');
      expect(passwordInput).toHaveAttribute('name', 'password');
      expect(passwordInput).toHaveAttribute('id', 'password-create');

      // Check confirm password input
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      expect(confirmPasswordInput).toHaveAttribute('type', 'password');
      expect(confirmPasswordInput).toHaveAttribute('placeholder', 'Enter confirm password');
      expect(confirmPasswordInput).toHaveAttribute('name', 'confirmPassword');
      expect(confirmPasswordInput).toHaveAttribute('id', 'confirm-password-create');
    });

    test('renders form with proper structure', () => {
      render(<AuthCreatePassword />);

      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();
      expect(form).toHaveAttribute('noValidate');
    });
  });

  describe('User Input Handling', () => {
    test('handles password input changes', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      await user.type(passwordInput, 'NewPassword123');

      expect(passwordInput).toHaveValue('NewPassword123');
    });

    test('handles confirm password input changes', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      await user.type(confirmPasswordInput, 'NewPassword123');

      expect(confirmPasswordInput).toHaveValue('NewPassword123');
    });
  });

  describe('Password Visibility Toggle', () => {
    test('toggles password visibility', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const toggleButtons = screen.getAllByRole('button', { name: /toggle password visibility/i });
      const passwordToggleButton = toggleButtons[0]; // First toggle button is for password

      // Initially password type
      expect(passwordInput).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(passwordToggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');

      // Click to hide password
      await user.click(passwordToggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
    });

    test('toggles confirm password visibility', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const toggleButtons = screen.getAllByRole('button', { name: /toggle password visibility/i });
      const confirmToggleButton = toggleButtons[1]; // Second toggle button is for confirm password

      // Initially password type
      expect(confirmPasswordInput).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(confirmToggleButton);
      expect(confirmPasswordInput).toHaveAttribute('type', 'text');

      // Click to hide password
      await user.click(confirmToggleButton);
      expect(confirmPasswordInput).toHaveAttribute('type', 'password');
    });

    test('displays correct icons for password visibility toggle', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const toggleButtons = screen.getAllByRole('button', { name: /toggle password visibility/i });
      const firstToggleButton = toggleButtons[0];

      // Initially shows EyeSlash (password hidden)
      expect(screen.getAllByTestId('eye-slash-icon')).toHaveLength(2);

      // Click to show password
      await user.click(firstToggleButton);
      expect(screen.getByTestId('eye-icon')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    test('shows validation errors for empty required fields', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const submitButton = screen.getByRole('button', { name: /create password/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/^password is required$/i)).toBeInTheDocument();
        expect(screen.getByText(/confirm password is required/i)).toBeInTheDocument();
      });
    });

    test('validates minimum password length', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'short');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });
    });

    test('validates password confirmation match', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'Password123');
      await user.type(confirmPasswordInput, 'DifferentPassword');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/both password must be match/i)).toBeInTheDocument();
      });
    });

    test('allows form submission when passwords match and meet requirements', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'Password123');
      await user.type(confirmPasswordInput, 'Password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('/api/create-password'),
          {
            newPassword: 'Password123'
          }
        );
      });
    });
  });

  describe('Form Submission', () => {
    test('calls create password API with correct data on successful submission', async () => {
      const user = userEvent.setup();
      mockAxios.post.mockResolvedValue({ data: {} });

      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'NewPassword123');
      await user.type(confirmPasswordInput, 'NewPassword123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('/api/create-password'),
          {
            newPassword: 'NewPassword123'
          }
        );
      });
    });

    test('shows success message and navigates on successful password creation', async () => {
      const user = userEvent.setup();
      mockAxios.post.mockResolvedValue({ data: {} });

      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'NewPassword123');
      await user.type(confirmPasswordInput, 'NewPassword123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          open: true,
          message: 'Password has been created successfully.',
          variant: 'alert',
          alert: { color: 'success' }
        });
      });

      // Check navigation after timeout
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/login', { replace: true });
      }, { timeout: 2000 });
    });

    test('disables submit button during form submission', async () => {
      const user = userEvent.setup();
      // Make axios call take some time to resolve
      mockAxios.post.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'NewPassword123');
      await user.type(confirmPasswordInput, 'NewPassword123');
      await user.click(submitButton);

      // Button should be disabled during submission
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    test('handles error when password creation fails', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Password creation failed';
      mockAxios.post.mockRejectedValue(new Error(errorMessage));

      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'NewPassword123');
      await user.type(confirmPasswordInput, 'NewPassword123');
      await user.click(submitButton);

      // Verify that the API was called and failed
      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('/api/create-password'),
          { newPassword: 'NewPassword123' }
        );
      });

      // Note: Component doesn't display submit errors in UI, but handles them internally
      expect(mockAxios.post).toHaveBeenCalledTimes(1);
    });

    test('handles error when scriptRef is null', async () => {
      const user = userEvent.setup();
      mockScriptRef.current = false; // Simulate component unmounted
      mockAxios.post.mockRejectedValue(new Error('Test error'));

      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      await user.type(passwordInput, 'NewPassword123');
      await user.type(confirmPasswordInput, 'NewPassword123');
      await user.click(submitButton);

      // Should not show error messages when component is unmounted
      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper form labels and ARIA attributes', () => {
      render(<AuthCreatePassword />);

      // Check that all inputs have proper labels
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^confirm password$/i)).toBeInTheDocument();

      // Check that form has proper structure
      const form = document.querySelector('form');
      expect(form).toHaveAttribute('noValidate');
    });

    test('has proper button labels and roles', () => {
      render(<AuthCreatePassword />);

      const submitButton = screen.getByRole('button', { name: /create password/i });
      expect(submitButton).toHaveAttribute('type', 'submit');

      const passwordToggleButtons = screen.getAllByRole('button', { name: /toggle password visibility/i });
      expect(passwordToggleButtons).toHaveLength(2);
      passwordToggleButtons.forEach(button => {
        expect(button).toHaveAttribute('aria-label', 'toggle password visibility');
      });
    });

    test('provides proper error messages with helper text', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const submitButton = screen.getByRole('button', { name: /create password/i });
      await user.click(submitButton);

      await waitFor(() => {
        // Check that error messages are displayed for accessibility
        expect(screen.getByText(/^password is required$/i)).toBeInTheDocument();
        expect(screen.getByText(/confirm password is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Props and Context Integration', () => {
    test('handles mouse down events on password toggles', () => {
      render(<AuthCreatePassword />);

      const passwordToggleButtons = screen.getAllByRole('button', { name: /toggle password visibility/i });
      const passwordToggleButton = passwordToggleButtons[0];
      const confirmPasswordToggleButton = passwordToggleButtons[1];

      // Should prevent default on mouse down
      fireEvent.mouseDown(passwordToggleButton);
      fireEvent.mouseDown(confirmPasswordToggleButton);

      // Component should still be functional
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^confirm password$/i)).toBeInTheDocument();
    });

    test('works with different scriptRef states', () => {
      const mockScriptRefContext = { current: false };
      useScriptRef.mockReturnValue(mockScriptRefContext);

      render(<AuthCreatePassword />);

      // Component should render properly with different scriptRef states
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
    });
  });

  describe('Input Field Behavior', () => {
    test('handles password input correctly', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      await user.type(passwordInput, 'TestPassword123');

      expect(passwordInput).toHaveValue('TestPassword123');
    });

    test('handles confirm password input correctly', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const confirmPasswordInput = screen.getByLabelText(/^confirm password$/i);
      await user.type(confirmPasswordInput, 'TestPassword123');

      expect(confirmPasswordInput).toHaveValue('TestPassword123');
    });

    test('validates password length on blur', async () => {
      const user = userEvent.setup();
      render(<AuthCreatePassword />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      await user.type(passwordInput, 'short');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });
    });
  });
});
