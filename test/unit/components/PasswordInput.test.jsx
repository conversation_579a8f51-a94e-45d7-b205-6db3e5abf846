import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';
import PasswordInput from '../../../src/sections/auth/components/PasswordInput';

// Create a test theme with customShadows
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    error: {
      main: '#d32f2f',
    },
    warning: {
      main: '#ed6c02',
    },
    info: {
      main: '#0288d1',
    },
    success: {
      main: '#2e7d32',
    },
  },
  customShadows: {
    primary: '0 0 0 2px rgba(25, 118, 210, 0.1)',
    secondary: '0 0 0 2px rgba(220, 0, 78, 0.2)',
    error: '0 0 0 2px rgba(211, 47, 47, 0.2)',
    warning: '0 0 0 2px rgba(237, 108, 2, 0.2)',
    info: '0 0 0 2px rgba(2, 136, 209, 0.2)',
    success: '0 0 0 2px rgba(46, 125, 50, 0.2)',
    primaryButton: '0 14px 12px rgba(25, 118, 210, 0.2)',
    secondaryButton: '0 14px 12px rgba(220, 0, 78, 0.2)',
    errorButton: '0 14px 12px rgba(211, 47, 47, 0.2)',
    warningButton: '0 14px 12px rgba(237, 108, 2, 0.2)',
    infoButton: '0 14px 12px rgba(2, 136, 209, 0.2)',
    successButton: '0 14px 12px rgba(46, 125, 50, 0.2)',
  },
});

const renderWithTheme = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('PasswordInput Component', () => {
  const defaultProps = {
    id: 'password',
    name: 'password',
    label: 'Password',
    value: '',
    onChange: jest.fn(),
    onBlur: jest.fn(),
    error: false,
    helperText: ''
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders password input with correct attributes', () => {
      renderWithTheme(<PasswordInput {...defaultProps} />);
      
      const input = screen.getByLabelText('Password');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'password');
      expect(input).toHaveAttribute('id', 'password');
      expect(input).toHaveAttribute('name', 'password');
    });

    test('renders with custom label', () => {
      renderWithTheme(<PasswordInput {...defaultProps} label="Custom Password" />);
      
      expect(screen.getByLabelText('Custom Password')).toBeInTheDocument();
    });

    test('renders with initial value', () => {
      renderWithTheme(<PasswordInput {...defaultProps} value="initial123" />);
      
      const input = screen.getByLabelText('Password');
      expect(input).toHaveValue('initial123');
    });

    test('renders toggle visibility button', () => {
      renderWithTheme(<PasswordInput {...defaultProps} />);
      
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      expect(toggleButton).toBeInTheDocument();
    });
  });

  describe('Password Visibility Toggle', () => {
    test('toggles password visibility on button click', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);
      
      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      
      // Initially password type
      expect(input).toHaveAttribute('type', 'password');
      
      // Click to show password
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'text');
      
      // Click to hide password again
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'password');
    });

    test('shows correct icon for password visibility state', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);

      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      const input = screen.getByLabelText('Password');

      // Initially password is hidden (type="password")
      expect(input).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'text');

      // Click to hide password
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'password');
    });

    test('maintains focus on input when toggle button is clicked', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

      // Focus the input first
      await user.click(input);
      expect(input).toHaveFocus();

      // Click the toggle button - focus should remain on input
      await user.click(toggleButton);
      expect(input).toHaveFocus();
    });
  });

  describe('Input Interactions', () => {
    test('calls onChange when input value changes', async () => {
      const mockOnChange = jest.fn();
      const user = userEvent.setup();
      
      renderWithTheme(<PasswordInput {...defaultProps} onChange={mockOnChange} />);
      
      const input = screen.getByLabelText('Password');
      await user.type(input, 'test123');
      
      expect(mockOnChange).toHaveBeenCalled();
    });

    test('calls onBlur when input loses focus', async () => {
      const mockOnBlur = jest.fn();
      const user = userEvent.setup();
      
      renderWithTheme(<PasswordInput {...defaultProps} onBlur={mockOnBlur} />);
      
      const input = screen.getByLabelText('Password');
      await user.click(input);
      await user.tab(); // Move focus away
      
      expect(mockOnBlur).toHaveBeenCalled();
    });

    test('maintains input value during visibility toggle', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} value="secret123" />);
      
      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      
      expect(input).toHaveValue('secret123');
      
      await user.click(toggleButton);
      expect(input).toHaveValue('secret123');
      expect(input).toHaveAttribute('type', 'text');
      
      await user.click(toggleButton);
      expect(input).toHaveValue('secret123');
      expect(input).toHaveAttribute('type', 'password');
    });
  });

  describe('Error States', () => {
    test('displays error state when error and touched are true', () => {
      renderWithTheme(
        <PasswordInput
          {...defaultProps}
          error="Password is required"
          touched={true}
        />
      );

      const input = screen.getByLabelText('Password');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    test('displays helper text when error and touched are true', () => {
      renderWithTheme(
        <PasswordInput
          {...defaultProps}
          error="Password is required"
          touched={true}
        />
      );

      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });

    test('does not display error when touched is false', () => {
      renderWithTheme(
        <PasswordInput
          {...defaultProps}
          error="Password is required"
          touched={false}
        />
      );

      const input = screen.getByLabelText('Password');
      expect(input).not.toHaveAttribute('aria-invalid', 'true');
      expect(screen.queryByText('Password is required')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      renderWithTheme(<PasswordInput {...defaultProps} />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

      expect(input).toHaveAttribute('id', 'password');
      expect(toggleButton).toHaveAttribute('aria-label', 'toggle password visibility');
    });

    test('associates helper text with input when error is shown', () => {
      renderWithTheme(
        <PasswordInput
          {...defaultProps}
          error="Enter your password"
          touched={true}
        />
      );

      const input = screen.getByLabelText('Password');
      const helperText = screen.getByText('Enter your password');

      expect(helperText).toHaveAttribute('id', 'helper-text-password');
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);
      
      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      
      // Tab to input
      await user.tab();
      expect(input).toHaveFocus();
      
      // Tab to toggle button
      await user.tab();
      expect(toggleButton).toHaveFocus();
      
      // Press Enter on toggle button
      await user.keyboard('{Enter}');
      expect(input).toHaveAttribute('type', 'text');
    });
  });

  describe('Props Validation', () => {
    test('handles missing optional props gracefully', () => {
      const minimalProps = {
        id: 'test',
        name: 'test',
        value: '',
        onChange: jest.fn()
      };
      
      expect(() => {
        renderWithTheme(<PasswordInput {...minimalProps} />);
      }).not.toThrow();
    });

    test('handles undefined values gracefully', () => {
      const propsWithUndefined = {
        ...defaultProps,
        value: undefined,
        helperText: undefined
      };
      
      expect(() => {
        renderWithTheme(<PasswordInput {...propsWithUndefined} />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    test('handles rapid toggle clicks', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

      // Initially password type
      expect(input).toHaveAttribute('type', 'password');

      // Rapid clicks (4 clicks = even number, should end up back to password)
      await user.click(toggleButton);
      await user.click(toggleButton);
      await user.click(toggleButton);
      await user.click(toggleButton);

      // Should end up in password mode (even number of clicks)
      expect(input).toHaveAttribute('type', 'password');
    });

    test('maintains focus on input during visibility toggle', async () => {
      const user = userEvent.setup();
      renderWithTheme(<PasswordInput {...defaultProps} />);
      
      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      
      await user.click(input);
      expect(input).toHaveFocus();

      await user.click(toggleButton);
      // Focus should remain on input after toggle
      expect(input).toHaveFocus();
    });
  });
});
