import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import EmailInput from '../../../src/sections/auth/components/EmailInput';

// Create a test theme
const theme = createTheme();

const renderWithTheme = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('EmailInput Component', () => {
  const defaultProps = {
    id: 'email',
    label: 'Email Address',
    name: 'email',
    value: '',
    error: '',
    touched: false,
    onChange: jest.fn(),
    onBlur: jest.fn(),
    placeholder: 'Enter email address'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders email input with correct attributes', () => {
      renderWithTheme(<EmailInput {...defaultProps} />);
      
      const input = screen.getByLabelText('Email Address');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'email');
      expect(input).toHaveAttribute('id', 'email');
      expect(input).toHaveAttribute('name', 'email');
      expect(input).toHaveAttribute('placeholder', 'Enter email address');
    });

    test('renders with custom label', () => {
      renderWithTheme(<EmailInput {...defaultProps} label="Custom Email" />);
      
      expect(screen.getByLabelText('Custom Email')).toBeInTheDocument();
    });

    test('renders with custom id and name', () => {
      renderWithTheme(
        <EmailInput 
          {...defaultProps} 
          id="user-email" 
          name="userEmail" 
          label="User Email"
        />
      );
      
      const input = screen.getByLabelText('User Email');
      expect(input).toHaveAttribute('id', 'user-email');
      expect(input).toHaveAttribute('name', 'userEmail');
    });

    test('renders with custom placeholder', () => {
      renderWithTheme(
        <EmailInput 
          {...defaultProps} 
          placeholder="<EMAIL>" 
        />
      );
      
      const input = screen.getByLabelText('Email Address');
      expect(input).toHaveAttribute('placeholder', '<EMAIL>');
    });

    test('renders with initial value', () => {
      renderWithTheme(<EmailInput {...defaultProps} value="<EMAIL>" />);
      
      const input = screen.getByLabelText('Email Address');
      expect(input).toHaveValue('<EMAIL>');
    });
  });

  describe('Input Interactions', () => {
    test('calls onChange when input value changes', async () => {
      const mockOnChange = jest.fn();
      const user = userEvent.setup();

      renderWithTheme(<EmailInput {...defaultProps} onChange={mockOnChange} />);

      const input = screen.getByLabelText('Email Address');
      await user.type(input, '<EMAIL>');

      expect(mockOnChange).toHaveBeenCalled();
    });

    test('calls onBlur when input loses focus', async () => {
      const mockOnBlur = jest.fn();
      const user = userEvent.setup();

      renderWithTheme(<EmailInput {...defaultProps} onBlur={mockOnBlur} />);

      const input = screen.getByLabelText('Email Address');
      await user.click(input);
      await user.tab(); // Move focus away

      expect(mockOnBlur).toHaveBeenCalled();
    });

    test('updates value when controlled', async () => {
      const { rerender } = renderWithTheme(
        <EmailInput {...defaultProps} value="" />
      );

      let input = screen.getByLabelText('Email Address');
      expect(input).toHaveValue('');

      // Simulate parent component updating the value
      rerender(
        <EmailInput {...defaultProps} value="test" />
      );

      await waitFor(() => {
        // Get fresh reference to the input after rerender
        input = screen.getByLabelText('Email Address');
        expect(input).toHaveAttribute('value', 'test');
      });
    });
  });

  describe('Error States', () => {
    test('displays error state when error and touched are true', () => {
      renderWithTheme(
        <EmailInput 
          {...defaultProps} 
          error="Email is required" 
          touched={true} 
        />
      );
      
      const input = screen.getByLabelText('Email Address');
      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });

    test('does not display error when touched is false', () => {
      renderWithTheme(
        <EmailInput 
          {...defaultProps} 
          error="Email is required" 
          touched={false} 
        />
      );
      
      const input = screen.getByLabelText('Email Address');
      expect(input).not.toHaveAttribute('aria-invalid', 'true');
      expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
    });

    test('does not display error when error is empty', () => {
      renderWithTheme(
        <EmailInput 
          {...defaultProps} 
          error="" 
          touched={true} 
        />
      );
      
      const input = screen.getByLabelText('Email Address');
      expect(input).not.toHaveAttribute('aria-invalid', 'true');
      expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
    });

    test('displays different error messages', () => {
      const errorMessages = [
        'Email is required',
        'Must be a valid email',
        'Email already exists'
      ];

      errorMessages.forEach(errorMessage => {
        const { rerender } = renderWithTheme(
          <EmailInput 
            {...defaultProps} 
            error={errorMessage} 
            touched={true} 
          />
        );
        
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
        
        // Clean up for next iteration
        rerender(<div />);
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      renderWithTheme(<EmailInput {...defaultProps} />);

      const input = screen.getByLabelText('Email Address');
      const label = screen.getByText('Email Address');

      expect(label).toHaveAttribute('for', 'email');
      // Note: OutlinedInput doesn't automatically add aria-label, it uses the label association
      expect(input).toHaveAttribute('id', 'email');
    });

    test('associates error message with input', () => {
      renderWithTheme(
        <EmailInput
          {...defaultProps}
          error="Email is required"
          touched={true}
        />
      );

      const input = screen.getByLabelText('Email Address');
      const errorMessage = screen.getByText('Email is required');

      // The error message should have the correct ID
      expect(errorMessage).toHaveAttribute('id', 'helper-text-email');
      // The input should be in error state
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      // Use uncontrolled component for user interaction testing
      const mockOnChange = jest.fn();
      renderWithTheme(<EmailInput
        {...defaultProps}
        value={undefined} // Make it uncontrolled
        onChange={mockOnChange}
      />);

      const input = screen.getByLabelText('Email Address');

      // Tab to input
      await user.tab();
      expect(input).toHaveFocus();

      // Type in input (this will work with uncontrolled component)
      await user.type(input, '<EMAIL>');
      expect(mockOnChange).toHaveBeenCalled();
    });
  });

  describe('Props Validation', () => {
    test('handles missing optional props gracefully', () => {
      const minimalProps = {
        value: '',
        onChange: jest.fn()
      };
      
      expect(() => {
        renderWithTheme(<EmailInput {...minimalProps} />);
      }).not.toThrow();
    });

    test('uses default values for missing props', () => {
      renderWithTheme(<EmailInput value="" onChange={jest.fn()} />);
      
      const input = screen.getByLabelText('Email Address'); // default label
      expect(input).toHaveAttribute('id', 'email'); // default id
      expect(input).toHaveAttribute('name', 'email'); // default name
      expect(input).toHaveAttribute('placeholder', 'Enter email address'); // default placeholder
    });

    test('handles undefined values gracefully', () => {
      const propsWithUndefined = {
        ...defaultProps,
        value: undefined,
        error: undefined
      };
      
      expect(() => {
        renderWithTheme(<EmailInput {...propsWithUndefined} />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    test('handles rapid input changes', async () => {
      const mockOnChange = jest.fn();
      const user = userEvent.setup();

      renderWithTheme(<EmailInput {...defaultProps} onChange={mockOnChange} />);

      const input = screen.getByLabelText('Email Address');

      // Rapid typing - each character triggers onChange
      await user.type(input, '<EMAIL>');

      // Should be called for each character typed (16 characters)
      expect(mockOnChange).toHaveBeenCalledTimes(16);
    });

    test('handles special characters in email', async () => {
      const user = userEvent.setup();

      // Use uncontrolled component for user interaction testing
      const mockOnChange = jest.fn();
      renderWithTheme(<EmailInput
        {...defaultProps}
        value={undefined} // Make it uncontrolled
        onChange={mockOnChange}
      />);

      const input = screen.getByLabelText('Email Address');
      await user.type(input, '<EMAIL>');

      expect(mockOnChange).toHaveBeenCalled();
      // Verify the onChange was called with the expected value
      const lastCall = mockOnChange.mock.calls[mockOnChange.mock.calls.length - 1];
      expect(lastCall[0].target.value).toBe('<EMAIL>');
    });

    test('maintains value during controlled updates', async () => {
      const { rerender } = renderWithTheme(
        <EmailInput {...defaultProps} value="<EMAIL>" />
      );

      let input = screen.getByLabelText('Email Address');
      expect(input).toHaveValue('<EMAIL>');

      // Update the value through props
      rerender(
        <EmailInput {...defaultProps} value="<EMAIL>" />
      );

      await waitFor(() => {
        // Get fresh reference to the input after rerender
        input = screen.getByLabelText('Email Address');
        expect(input).toHaveAttribute('value', '<EMAIL>');
      });
    });
  });

  describe('Integration with Forms', () => {
    test('works with form submission', async () => {
      const mockSubmit = jest.fn();
      const user = userEvent.setup();
      
      renderWithTheme(
        <form onSubmit={mockSubmit}>
          <EmailInput {...defaultProps} />
          <button type="submit">Submit</button>
        </form>
      );
      
      const input = screen.getByLabelText('Email Address');
      const submitButton = screen.getByRole('button', { name: 'Submit' });
      
      await user.type(input, '<EMAIL>');
      await user.click(submitButton);
      
      expect(mockSubmit).toHaveBeenCalled();
    });
  });
});
