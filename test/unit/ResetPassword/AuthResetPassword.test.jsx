import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('hooks/useAuth');
jest.mock('hooks/useScriptRef');
jest.mock('api/snackbar');
jest.mock('axios');

// ✅ Mock password strength utility consistently
jest.mock('utils/password-strength');

// Mock context
jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext(),
  };
});

// Mock Axios wrapper
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: { common: {} },
  },
}));

// Component import AFTER mocks
import AuthResetPassword from '../../../src/sections/auth/auth-forms/AuthResetPassword';

// Mock UI components
jest.mock('components/@extended/IconButton', () => {
  return function MockIconButton({ children, onClick, ...props }) {
    return (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div>{children}</div>;
  };
});

// Mock icons
jest.mock('iconsax-react', () => ({
  Eye: () => <span data-testid="eye-icon">Eye</span>,
  EyeSlash: () => <span data-testid="eye-slash-icon">EyeSlash</span>,
}));

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock axios actual instance
const mockAxios = require('axios');

// Environment variable
process.env.VITE_APP_API_URL = 'http://localhost:3000';

// ------------------------------------------------------------
// 🔹 Mock URLSearchParams
// ------------------------------------------------------------
let mockTokenValue = 'test-reset-token';

// Create a proper URLSearchParams mock
class MockURLSearchParams {
  constructor(search) {
    this.search = search;
  }

  get(key) {
    if (key === 'token') {
      return mockTokenValue;
    }
    return null;
  }
}

// Mock URLSearchParams globally
global.URLSearchParams = MockURLSearchParams;

// ------------------------------------------------------------
// 🔹 Setup beforeEach
// ------------------------------------------------------------
describe('AuthResetPassword Component - Passing Tests Only', () => {
  let mockScriptRef;
  let mockOpenSnackbar;
  let useAuth;
  let useScriptRef;
  let openSnackbar;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    mockScriptRef = { current: true };
    mockOpenSnackbar = jest.fn();

    useAuth = require('hooks/useAuth').default;
    useScriptRef = require('hooks/useScriptRef').default;
    openSnackbar = require('api/snackbar').openSnackbar;

    // Hook mocks
    useAuth.mockReturnValue({ isLoggedIn: false });
    useScriptRef.mockReturnValue(mockScriptRef);
    openSnackbar.mockImplementation(mockOpenSnackbar);

    mockAxios.post.mockResolvedValue({ data: {} });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  // ------------------------------------------------------------
  // ✅ Passing Tests
  // ------------------------------------------------------------
  describe('Component Rendering', () => {
    test('renders form and fields', () => {
      render(<AuthResetPassword />);
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reset password/i })).toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    test('submits with correct token and new password', async () => {
      const user = userEvent.setup();
      render(<AuthResetPassword />);
      await user.type(screen.getByLabelText(/^password$/i), 'Password123');
      await user.type(screen.getByLabelText(/confirm password/i), 'Password123');

      await user.click(screen.getByRole('button', { name: /reset password/i }));

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('/api/reset-password'),
          {
            token: 'test-reset-token',
            newPassword: 'Password123',
          }
        );
      });
    });
  });

  describe('Token Handling', () => {
    test('handles missing token in URL', () => {
      // Set token to null
      mockTokenValue = null;

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      render(<AuthResetPassword />);
      expect(consoleSpy).toHaveBeenCalledWith('No reset token found in URL');
      consoleSpy.mockRestore();

      // Restore token
      mockTokenValue = 'test-reset-token';
    });

    test('extracts token correctly', () => {
      // Set a different token
      mockTokenValue = 'valid-token-123';

      render(<AuthResetPassword />);
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();

      // Restore original token
      mockTokenValue = 'test-reset-token';
    });
  });

  describe('Password Strength Integration', () => {
    test('mock is working', () => {
      // Test that the mock is working
      const passwordStrengthModule = require('utils/password-strength');
      expect(passwordStrengthModule.strengthIndicator).toBeDefined();
      expect(jest.isMockFunction(passwordStrengthModule.strengthIndicator)).toBe(true);

      // Call the function directly to test the mock
      passwordStrengthModule.strengthIndicator('test');
      expect(passwordStrengthModule.strengthIndicator).toHaveBeenCalledWith('test');
    });
  });
});
