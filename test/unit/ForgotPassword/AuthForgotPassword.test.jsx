import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('hooks/useAuth');
jest.mock('hooks/useScriptRef');
jest.mock('api/snackbar');

jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext()
  };
});

// Mock utils/axios to prevent import.meta issues
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: {
      common: {}
    }
  }
}));

// Now import the component after mocking dependencies
import AuthForgotPassword from '../../../src/sections/auth/auth-forms/AuthForgotPassword';

// Mock the components
jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div>{children}</div>;
  };
});

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

describe('AuthForgotPassword Component - Comprehensive Tests', () => {
  let mockResetPassword;
  let mockScriptRef;
  let mockOpenSnackbar;
  let useAuth;
  let useScriptRef;
  let openSnackbar;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock functions
    mockResetPassword = jest.fn();
    mockScriptRef = { current: true };
    mockOpenSnackbar = jest.fn();

    // Mock the hooks and utilities
    useAuth = require('hooks/useAuth').default;
    useScriptRef = require('hooks/useScriptRef').default;
    openSnackbar = require('api/snackbar').openSnackbar;

    useAuth.mockReturnValue({
      isLoggedIn: false,
      resetPassword: mockResetPassword
    });

    useScriptRef.mockReturnValue(mockScriptRef);
    openSnackbar.mockImplementation(mockOpenSnackbar);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders forgot password form with all required fields', () => {
      render(<AuthForgotPassword />);

      // Check for email input
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /send password reset email/i })).toBeInTheDocument();

      // Check for SPAM notice
      expect(screen.getByText(/do not forgot to check spam box/i)).toBeInTheDocument();
    });

    test('renders input fields with correct attributes', () => {
      render(<AuthForgotPassword />);

      // Check email input
      const emailInput = screen.getByLabelText(/email address/i);
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('placeholder', 'Enter email address');
      expect(emailInput).toHaveAttribute('name', 'email');
      expect(emailInput).toHaveAttribute('id', 'email-forgot');
    });

    test('renders form with proper structure', () => {
      render(<AuthForgotPassword />);

      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();
      expect(form).toHaveAttribute('noValidate');
    });

    test('renders submit button with correct styling', () => {
      render(<AuthForgotPassword />);

      const submitButton = screen.getByRole('button', { name: /send password reset email/i });
      expect(submitButton).toHaveAttribute('type', 'submit');
      // Note: variant is a Material-UI prop, not an HTML attribute
      expect(submitButton).toBeInTheDocument();
    });
  });

  describe('User Input Handling', () => {
    test('handles email input changes', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, '<EMAIL>');

      expect(emailInput).toHaveValue('<EMAIL>');
    });

    test('handles email input with various formats', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      
      // Test different email formats
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      expect(emailInput).toHaveValue('<EMAIL>');
    });
  });

  describe('Form Validation', () => {
    test('shows validation errors for empty email field', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const submitButton = screen.getByRole('button', { name: /send password reset email/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    test('validates email format', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, 'invalid-email');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });
    });

    test('validates email format on blur', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, 'invalid-email');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });
    });

    test('allows form submission with valid email', async () => {
      const user = userEvent.setup();
      mockResetPassword.mockResolvedValue({});
      
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockResetPassword).toHaveBeenCalledWith('<EMAIL>');
      });
    });
  });

  describe('Form Submission', () => {
    test('calls resetPassword function with correct email on successful submission', async () => {
      const user = userEvent.setup();
      mockResetPassword.mockResolvedValue({});
      
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockResetPassword).toHaveBeenCalledWith('<EMAIL>');
      });
    });

    test('shows success message and navigates on successful password reset request', async () => {
      const user = userEvent.setup();
      mockResetPassword.mockResolvedValue({});
      
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOpenSnackbar).toHaveBeenCalledWith({
          open: true,
          message: 'Check mail for reset password link',
          variant: 'alert',
          alert: {
            color: 'success'
          }
        });
      });

      // Check navigation after timeout
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/check-mail', { replace: true });
      }, { timeout: 2000 });
    });

    test('navigates to auth route when user is logged in', async () => {
      const user = userEvent.setup();
      mockResetPassword.mockResolvedValue({});
      
      useAuth.mockReturnValue({
        isLoggedIn: true,
        resetPassword: mockResetPassword
      });
      
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/auth/check-mail', { replace: true });
      }, { timeout: 2000 });
    });

    test('disables submit button during form submission', async () => {
      const user = userEvent.setup();
      // Make resetPassword function take some time to resolve
      mockResetPassword.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      // Button should be disabled during submission
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    test('displays error message when password reset fails', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Email not found';
      mockResetPassword.mockRejectedValue(new Error(errorMessage));

      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });

    test('handles error when scriptRef is null', async () => {
      const user = userEvent.setup();
      mockScriptRef.current = false; // Simulate component unmounted
      mockResetPassword.mockRejectedValue(new Error('Test error'));

      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      // Should not show error messages when component is unmounted
      await waitFor(() => {
        expect(mockResetPassword).toHaveBeenCalled();
      });
    });

    test('handles resetPassword promise rejection', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Network error';
      mockResetPassword.mockImplementation(() => Promise.reject(new Error(errorMessage)));

      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });

    test('handles general catch block errors', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Unexpected error';
      // Mock resetPassword to throw an error that bypasses the .then() chain
      mockResetPassword.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      await user.type(emailInput, '<EMAIL>');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper form labels and ARIA attributes', () => {
      render(<AuthForgotPassword />);

      // Check that email input has proper label
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();

      // Check that form has proper structure
      const form = document.querySelector('form');
      expect(form).toHaveAttribute('noValidate');
    });

    test('has proper button labels and roles', () => {
      render(<AuthForgotPassword />);

      const submitButton = screen.getByRole('button', { name: /send password reset email/i });
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    test('provides proper error messages with helper text', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const submitButton = screen.getByRole('button', { name: /send password reset email/i });
      await user.click(submitButton);

      await waitFor(() => {
        // Check that error messages are displayed for accessibility
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    test('has proper helper text IDs for accessibility', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, 'invalid-email');
      await user.tab();

      await waitFor(() => {
        const helperText = screen.getByText(/must be a valid email/i);
        expect(helperText).toHaveAttribute('id', 'helper-text-email-forgot');
      });
    });
  });

  describe('Props and Context Integration', () => {
    test('works with different auth context states', () => {
      const mockAuthContext = {
        isLoggedIn: true,
        resetPassword: mockResetPassword
      };

      useAuth.mockReturnValue(mockAuthContext);

      render(<AuthForgotPassword />);

      // Component should render properly with different auth states
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    });

    test('works with different scriptRef states', () => {
      const mockScriptRefContext = { current: false };
      useScriptRef.mockReturnValue(mockScriptRefContext);

      render(<AuthForgotPassword />);

      // Component should render properly with different scriptRef states
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    });
  });

  describe('Input Field Behavior', () => {
    test('handles email input correctly', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, '<EMAIL>');

      expect(emailInput).toHaveValue('<EMAIL>');
    });

    test('validates email on blur event', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, 'invalid.email');
      await user.tab(); // Trigger blur

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });
    });

    test('clears validation error when valid email is entered', async () => {
      const user = userEvent.setup();
      render(<AuthForgotPassword />);

      const emailInput = screen.getByLabelText(/email address/i);

      // First enter invalid email
      await user.type(emailInput, 'invalid');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });

      // Then enter valid email
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText(/must be a valid email/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('UI Elements', () => {
    test('displays SPAM notice text', () => {
      render(<AuthForgotPassword />);

      expect(screen.getByText(/do not forgot to check spam box/i)).toBeInTheDocument();
    });

    test('renders submit button with correct text and styling', () => {
      render(<AuthForgotPassword />);

      const submitButton = screen.getByRole('button', { name: /send password reset email/i });
      expect(submitButton).toBeInTheDocument();
      // Note: variant is a Material-UI prop, not an HTML attribute
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    test('form layout renders correctly', () => {
      render(<AuthForgotPassword />);

      // Check that the form contains the expected elements in order
      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();

      const emailInput = screen.getByLabelText(/email address/i);
      const spamNotice = screen.getByText(/do not forgot to check spam box/i);
      const submitButton = screen.getByRole('button', { name: /send password reset email/i });

      expect(emailInput).toBeInTheDocument();
      expect(spamNotice).toBeInTheDocument();
      expect(submitButton).toBeInTheDocument();
    });
  });
});
