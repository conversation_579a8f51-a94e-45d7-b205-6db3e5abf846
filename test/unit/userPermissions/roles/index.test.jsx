import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import axios from 'axios';
import ParentComponent from '../../../../src/pages/userPermissions/roles/index';

// Mock dependencies
jest.mock('axios');
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

// Mock Spinner component to avoid image import issues
jest.mock('../../../../src/components/custom-components/Spinner', () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

// Mock RBAC context
jest.mock('pages/permissions/RBACContext', () => ({
  useRBAC: () => ({
    canMenuPage: jest.fn(() => true), // Mock to return true for all permissions
  }),
}));

// Mock config module
jest.mock('config', () => ({
  ThemeMode: {
    DARK: 'dark',
    LIGHT: 'light',
  },
}), { virtual: true });

// Mock constants module
jest.mock('constants', () => ({
  PERMISSIONS: {
    FULL_ACCESS: 15,
    READ: 1,
    WRITE: 2,
    EXECUTE: 4,
    DELETE: 8,
  },
  MENUS: {
    LEFT: 'Left_Menu',
    TOP: 'Top_Menu',
  },
  PAGES: {
    ROLES: 'Roles',
  },
}));

// Mock react-dnd
jest.mock('react-dnd', () => ({
  useDrag: () => [null, null, null],
  useDrop: () => [null, null],
  DndProvider: ({ children }) => children,
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}));

jest.mock('pages/permissions/RBACContext', () => ({
  useRBAC: () => ({
    canMenuPage: jest.fn(() => true),
  }),
}));

jest.mock('pages/jobrequest/jorequestdeletedialog', () => {
  return function MockConfirmDeleteDialog({ open, handleClose, handleConfirm, isActive, entityType }) {
    return open ? (
      <div data-testid="confirm-delete-dialog">
        <div>Delete {entityType}</div>
        <div>Status: {isActive ? 'Active' : 'Inactive'}</div>
        <button onClick={handleClose}>Cancel</button>
        <button onClick={handleConfirm}>Confirm</button>
      </div>
    ) : null;
  };
});

jest.mock('components/custom-components/CustomTableContainer', () => {
  return function MockCustomTableContainer({ 
    table, 
    onAddClick, 
    csvFilename, 
    showAddButton, 
    addLabel, 
    data, 
    rowSelection, 
    theme 
  }) {
    return (
      <div data-testid="custom-table-container">
        <div data-testid="table-data">{JSON.stringify(data)}</div>
        {showAddButton && (
          <button onClick={onAddClick} data-testid="add-button">
            Add {addLabel}
          </button>
        )}
        <div data-testid="csv-filename">{csvFilename}</div>
        <div data-testid="row-selection">{JSON.stringify(rowSelection)}</div>
        {/* Mock table rows */}
        {data.map((row, index) => (
          <div key={index} data-testid={`table-row-${index}`}>
            <span>{row.role_name}</span>
            <span>{row.level_name}</span>
            <span>{row.description}</span>
            <span>{row.is_active ? 'Active' : 'Inactive'}</span>
            <button 
              onClick={() => {
                // Simulate menu actions
                const event = new Event('click');
                Object.defineProperty(event, 'stopPropagation', { value: jest.fn() });
                // Mock menu click behavior
              }}
              data-testid={`actions-menu-${index}`}
            >
              Actions
            </button>
          </div>
        ))}
      </div>
    );
  };
});

const mockAxios = axios;;

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('ParentComponent (Roles Index)', () => {
  const mockRolesData = [
    {
      id: '1',
      role_name: 'Admin',
      level_name: 'Level 1',
      description: 'Administrator role',
      ParentRole: { role_name: 'Super Admin' },
      is_active: true,
    },
    {
      id: '2',
      role_name: 'User',
      level_name: 'Level 2',
      description: 'Regular user role',
      ParentRole: { role_name: 'Admin' },
      is_active: false,
    },
  ];

  beforeEach(() => {
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'mock-token'),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });

    // Mock import.meta.env
    Object.defineProperty(import.meta, 'env', {
      value: {
        VITE_APP_API_URL: 'http://localhost:8080',
      },
      writable: true,
    });

    mockAxios.get.mockResolvedValue({
      data: mockRolesData,
    });

    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders without crashing', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(screen.getByTestId('custom-table-container')).toBeInTheDocument();
      });
    });

    test('displays loading state initially', () => {
      renderWithProviders(<ParentComponent />);
      
      // Component should render even during loading
      expect(screen.getByTestId('custom-table-container')).toBeInTheDocument();
    });

    test('renders table with roles data after loading', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        const tableData = screen.getByTestId('table-data');
        expect(tableData).toHaveTextContent('Admin');
        expect(tableData).toHaveTextContent('User');
      });
    });
  });

  describe('API Integration', () => {
    test('fetches roles data on component mount', async () => {
      renderWithProviders(<ParentComponent />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('http://localhost:3000/roles', {
          headers: {
            Authorization: 'Bearer mock-token',
            'Content-Type': 'application/json',
          },
        });
      });
    });

    test('handles API error gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.get.mockRejectedValue(new Error('API Error'));
      
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error fetching roles:', expect.any(Error));
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Table Functionality', () => {
    test('displays correct CSV filename', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(screen.getByTestId('csv-filename')).toHaveTextContent('Roles-list.csv');
      });
    });

    test('shows add button when user has permissions', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(screen.getByTestId('add-button')).toBeInTheDocument();
        expect(screen.getByTestId('add-button')).toHaveTextContent('Add Roles');
      });
    });

    test('displays table rows with role data', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(screen.getByTestId('table-row-0')).toHaveTextContent('Admin');
        expect(screen.getByTestId('table-row-0')).toHaveTextContent('Level 1');
        expect(screen.getByTestId('table-row-0')).toHaveTextContent('Administrator role');
        expect(screen.getByTestId('table-row-0')).toHaveTextContent('Active');
        
        expect(screen.getByTestId('table-row-1')).toHaveTextContent('User');
        expect(screen.getByTestId('table-row-1')).toHaveTextContent('Level 2');
        expect(screen.getByTestId('table-row-1')).toHaveTextContent('Regular user role');
        expect(screen.getByTestId('table-row-1')).toHaveTextContent('Inactive');
      });
    });
  });

  describe('Delete Functionality', () => {
    test('confirm delete dialog is not visible initially', async () => {
      renderWithProviders(<ParentComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('table-row-0')).toBeInTheDocument();
      });

      expect(screen.queryByTestId('confirm-delete-dialog')).not.toBeInTheDocument();
    });

    test('handles delete confirmation API call', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        json: () => Promise.resolve({ success: true }),
      });

      renderWithProviders(<ParentComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('table-row-0')).toBeInTheDocument();
      });

      // Test would involve triggering delete through the actual component
      // For now, we verify the fetch mock is available
      expect(global.fetch).toBeDefined();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', async () => {
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        const addButton = screen.getByTestId('add-button');
        expect(addButton).toBeInTheDocument();
      });
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        const addButton = screen.getByTestId('add-button');
        expect(addButton).toBeInTheDocument();
      });
      
      const addButton = screen.getByTestId('add-button');
      await user.tab();
      // Button should be focusable
      expect(document.activeElement).toBe(addButton);
    });
  });

  describe('Responsive Design', () => {
    test('adapts to mobile viewport', async () => {
      // Mock mobile breakpoint
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query.includes('(max-width: 600px)'),
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });
      
      renderWithProviders(<ParentComponent />);
      
      await waitFor(() => {
        expect(screen.getByTestId('custom-table-container')).toBeInTheDocument();
      });
    });
  });

  describe('Utility Functions', () => {
    test('fuzzyFilter function works correctly', () => {
      const { fuzzyFilter } = require('../../../../src/pages/userPermissions/roles/index');
      
      const mockRow = {
        getValue: jest.fn(() => 'Admin Role'),
      };
      const mockAddMeta = jest.fn();
      
      const result = fuzzyFilter(mockRow, 'role_name', 'admin', mockAddMeta);
      
      expect(mockRow.getValue).toHaveBeenCalledWith('role_name');
      expect(mockAddMeta).toHaveBeenCalled();
      expect(typeof result).toBe('boolean');
    });

    test('fuzzySort function works correctly', () => {
      const { fuzzySort } = require('../../../../src/pages/userPermissions/roles/index');
      
      const mockRowA = {
        columnFiltersMeta: { role_name: { rank: 1 } },
      };
      const mockRowB = {
        columnFiltersMeta: { role_name: { rank: 2 } },
      };
      
      const result = fuzzySort(mockRowA, mockRowB, 'role_name');
      expect(typeof result).toBe('number');
    });
  });
});
