import data from '../../../../src/pages/userPermissions/roles/staticdata';

describe('Static Data Module', () => {
  describe('Data Structure Validation', () => {
    test('exports an array', () => {
      expect(Array.isArray(data)).toBe(true);
    });

    test('contains expected number of role entries', () => {
      expect(data).toHaveLength(2);
    });

    test('each role has required properties', () => {
      data.forEach((role, index) => {
        expect(role).toHaveProperty('name');
        expect(role).toHaveProperty('roleType');
        expect(role).toHaveProperty('description');
        expect(role).toHaveProperty('createdBy');
        expect(role).toHaveProperty('updatedBy');
        expect(role).toHaveProperty('status');
        expect(role).toHaveProperty('menuStructure');
        
        expect(typeof role.name).toBe('string');
        expect(typeof role.roleType).toBe('string');
        expect(typeof role.description).toBe('string');
        expect(typeof role.createdBy).toBe('string');
        expect(typeof role.updatedBy).toBe('string');
        expect(typeof role.status).toBe('string');
        expect(Array.isArray(role.menuStructure)).toBe(true);
      });
    });
  });

  describe('Admin Role Data', () => {
    const adminRole = data.find(role => role.name === 'Admin');

    test('Admin role exists', () => {
      expect(adminRole).toBeDefined();
    });

    test('Admin role has correct basic properties', () => {
      expect(adminRole.name).toBe('Admin');
      expect(adminRole.roleType).toBe('Admin');
      expect(adminRole.description).toBe('Administrator role');
      expect(adminRole.createdBy).toBe('adminUserId');
      expect(adminRole.updatedBy).toBe('adminUserId');
      expect(adminRole.status).toBe('Active');
    });

    test('Admin role has menu structure', () => {
      expect(adminRole.menuStructure).toHaveLength(1);
      expect(adminRole.menuStructure[0].name).toBe('Left_Menu');
    });

    test('Admin role Left_Menu has children', () => {
      const leftMenu = adminRole.menuStructure[0];
      expect(Array.isArray(leftMenu.children)).toBe(true);
      expect(leftMenu.children).toHaveLength(1);
      expect(leftMenu.children[0].name).toBe('Client');
    });

    test('Admin role Client page has correct structure', () => {
      const clientPage = adminRole.menuStructure[0].children[0];
      expect(clientPage.name).toBe('Client');
      expect(clientPage.type).toBe('PAGE');
      expect(clientPage.permissions).toBe(15);
      expect(clientPage.displayNumber).toBe(9);
      expect(Array.isArray(clientPage.children)).toBe(true);
      expect(clientPage.children).toHaveLength(4);
    });

    test('Admin role Client actions have correct structure', () => {
      const clientActions = adminRole.menuStructure[0].children[0].children;
      
      const expectedActions = [
        { name: 'Add_Client', permissions: 15, displayNumber: 1 },
        { name: 'Edit', permissions: 15, displayNumber: 2 },
        { name: 'Update', permissions: 15, displayNumber: 3 },
        { name: 'Deactivate', permissions: 15, displayNumber: 4 },
      ];

      expectedActions.forEach((expectedAction, index) => {
        const action = clientActions[index];
        expect(action.name).toBe(expectedAction.name);
        expect(action.type).toBe('ACTION');
        expect(action.permissions).toBe(expectedAction.permissions);
        expect(action.displayNumber).toBe(expectedAction.displayNumber);
      });
    });
  });

  describe('Superadmin Role Data', () => {
    const superadminRole = data.find(role => role.name === 'Superadmin');

    test('Superadmin role exists', () => {
      expect(superadminRole).toBeDefined();
    });

    test('Superadmin role has correct basic properties', () => {
      expect(superadminRole.name).toBe('Superadmin');
      expect(superadminRole.roleType).toBe('Superadmin');
      expect(superadminRole.description).toBe('Administrator role');
      expect(superadminRole.createdBy).toBe('adminUserId');
      expect(superadminRole.updatedBy).toBe('adminUserId');
      expect(superadminRole.status).toBe('Active');
    });

    test('Superadmin role has multiple menu structures', () => {
      expect(superadminRole.menuStructure).toHaveLength(2);
      
      const menuNames = superadminRole.menuStructure.map(menu => menu.name);
      expect(menuNames).toContain('Left_Menu');
      expect(menuNames).toContain('Top_Menu');
    });

    test('Superadmin Left_Menu structure', () => {
      const leftMenu = superadminRole.menuStructure.find(menu => menu.name === 'Left_Menu');
      expect(leftMenu).toBeDefined();
      expect(leftMenu.children).toHaveLength(1);
      expect(leftMenu.children[0].name).toBe('Client');
    });

    test('Superadmin Top_Menu structure', () => {
      const topMenu = superadminRole.menuStructure.find(menu => menu.name === 'Top_Menu');
      expect(topMenu).toBeDefined();
      expect(topMenu.children).toHaveLength(1);
      expect(topMenu.children[0].name).toBe('Clientssssssss');
    });

    test('Superadmin Top_Menu has extended naming convention', () => {
      const topMenu = superadminRole.menuStructure.find(menu => menu.name === 'Top_Menu');
      const clientPage = topMenu.children[0];
      
      expect(clientPage.name).toBe('Clientssssssss');
      expect(clientPage.type).toBe('PAGE');
      expect(clientPage.permissions).toBe(15);
      expect(clientPage.displayNumber).toBe(9);
      
      const expectedActions = [
        'Add_Clientssssssss',
        'Editssssssssss',
        'Updatesssssssss',
        'Deactivatesssssssss',
      ];

      clientPage.children.forEach((action, index) => {
        expect(action.name).toBe(expectedActions[index]);
        expect(action.type).toBe('ACTION');
        expect(action.permissions).toBe(15);
      });
    });
  });

  describe('Permission Values', () => {
    test('all permissions are set to 15 (full permissions)', () => {
      data.forEach(role => {
        role.menuStructure.forEach(menu => {
          menu.children.forEach(page => {
            expect(page.permissions).toBe(15);
            
            page.children.forEach(action => {
              expect(action.permissions).toBe(15);
            });
          });
        });
      });
    });

    test('permission value 15 represents all CRUD operations', () => {
      // 15 in binary is 1111, which represents:
      // 1 (read) + 2 (create) + 4 (update) + 8 (delete) = 15
      const fullPermissions = 15;
      
      // Test bitwise operations
      expect(fullPermissions & 1).toBe(1); // Read permission
      expect(fullPermissions & 2).toBe(2); // Create permission
      expect(fullPermissions & 4).toBe(4); // Update permission
      expect(fullPermissions & 8).toBe(8); // Delete permission
    });
  });

  describe('Display Numbers', () => {
    test('display numbers are properly set for ordering', () => {
      data.forEach(role => {
        role.menuStructure.forEach(menu => {
          menu.children.forEach(page => {
            expect(typeof page.displayNumber).toBe('number');
            expect(page.displayNumber).toBeGreaterThan(0);
            
            page.children.forEach(action => {
              expect(typeof action.displayNumber).toBe('number');
              expect(action.displayNumber).toBeGreaterThan(0);
            });
          });
        });
      });
    });

    test('action display numbers are sequential', () => {
      data.forEach(role => {
        role.menuStructure.forEach(menu => {
          menu.children.forEach(page => {
            const displayNumbers = page.children.map(action => action.displayNumber);
            const sortedNumbers = [...displayNumbers].sort((a, b) => a - b);
            
            expect(displayNumbers).toEqual(sortedNumbers);
            
            // Check if numbers are sequential starting from 1
            sortedNumbers.forEach((num, index) => {
              expect(num).toBe(index + 1);
            });
          });
        });
      });
    });
  });

  describe('Data Consistency', () => {
    test('all roles have consistent structure', () => {
      const firstRoleKeys = Object.keys(data[0]).sort();
      
      data.forEach(role => {
        const roleKeys = Object.keys(role).sort();
        expect(roleKeys).toEqual(firstRoleKeys);
      });
    });

    test('menu structures have consistent format', () => {
      data.forEach(role => {
        role.menuStructure.forEach(menu => {
          expect(menu).toHaveProperty('name');
          expect(menu).toHaveProperty('children');
          expect(Array.isArray(menu.children)).toBe(true);
          
          menu.children.forEach(page => {
            expect(page).toHaveProperty('name');
            expect(page).toHaveProperty('type');
            expect(page).toHaveProperty('permissions');
            expect(page).toHaveProperty('displayNumber');
            expect(page).toHaveProperty('children');
            expect(page.type).toBe('PAGE');
            
            page.children.forEach(action => {
              expect(action).toHaveProperty('name');
              expect(action).toHaveProperty('type');
              expect(action).toHaveProperty('permissions');
              expect(action).toHaveProperty('displayNumber');
              expect(action.type).toBe('ACTION');
            });
          });
        });
      });
    });
  });

  describe('Data Immutability', () => {
    test('exported data should not be modified', () => {
      const originalLength = data.length;
      const originalFirstRoleName = data[0].name;
      
      // Attempt to modify (should not affect original)
      const dataCopy = [...data];
      dataCopy.push({ name: 'Test Role' });
      
      expect(data).toHaveLength(originalLength);
      expect(data[0].name).toBe(originalFirstRoleName);
    });

    test('nested objects should maintain integrity', () => {
      const originalMenuLength = data[0].menuStructure.length;
      const originalMenuName = data[0].menuStructure[0].name;
      
      // Create a copy and modify it
      const roleCopy = JSON.parse(JSON.stringify(data[0]));
      roleCopy.menuStructure[0].name = 'Modified Menu';
      
      expect(data[0].menuStructure).toHaveLength(originalMenuLength);
      expect(data[0].menuStructure[0].name).toBe(originalMenuName);
    });
  });
});
