import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import DialogData from '../../../../src/pages/userPermissions/roles/DialogData';

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('DialogData Component', () => {
  const mockSiteMapData = [
    {
      name: 'Left_Menu',
      children: [
        {
          name: 'Dashboard',
          type: 'PAGE',
          permissions: 15,
          displayNumber: 1,
          children: [],
        },
      ],
    },
  ];

  const mockOnPermissionsChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders without crashing', () => {
      expect(() => {
        renderWithProviders(
          <DialogData
            siteMapData={mockSiteMapData}
            onPermissionsChange={mockOnPermissionsChange}
          />
        );
      }).not.toThrow();
    });

    test('renders with valid props', () => {
      renderWithProviders(
        <DialogData
          siteMapData={mockSiteMapData}
          onPermissionsChange={mockOnPermissionsChange}
        />
      );

      // Component should render without errors
      expect(document.body).toBeInTheDocument();
    });

    test('handles empty siteMapData', () => {
      expect(() => {
        renderWithProviders(
          <DialogData
            siteMapData={[]}
            onPermissionsChange={mockOnPermissionsChange}
          />
        );
      }).not.toThrow();
    });

    test('handles missing onPermissionsChange callback', () => {
      expect(() => {
        renderWithProviders(
          <DialogData
            siteMapData={mockSiteMapData}
          />
        );
      }).not.toThrow();
    });
  });

  describe('Data Handling', () => {
    test('processes siteMapData correctly', () => {
      renderWithProviders(
        <DialogData
          siteMapData={mockSiteMapData}
          onPermissionsChange={mockOnPermissionsChange}
        />
      );

      // Component should process the data without errors
      expect(mockSiteMapData).toHaveLength(1);
      expect(mockSiteMapData[0].name).toBe('Left_Menu');
    });

    test('handles complex siteMapData structure', () => {
      const complexData = [
        {
          name: 'Menu1',
          children: [
            {
              name: 'Page1',
              type: 'PAGE',
              permissions: 15,
              displayNumber: 1,
              children: [
                {
                  name: 'Action1',
                  type: 'ACTION',
                  permissions: 1,
                  displayNumber: 1,
                },
              ],
            },
          ],
        },
      ];

      expect(() => {
        renderWithProviders(
          <DialogData
            siteMapData={complexData}
            onPermissionsChange={mockOnPermissionsChange}
          />
        );
      }).not.toThrow();
    });
  });

  describe('Permission Values', () => {
    test('handles permission calculations', () => {
      // Test basic permission value handling
      const permissions = 15; // All permissions

      // Test bitwise operations for permissions
      expect(permissions & 1).toBe(1); // Read
      expect(permissions & 2).toBe(2); // Create
      expect(permissions & 4).toBe(4); // Update
      expect(permissions & 8).toBe(8); // Delete
    });

    test('validates permission structure', () => {
      const testData = mockSiteMapData[0].children[0];

      expect(testData).toHaveProperty('name');
      expect(testData).toHaveProperty('type');
      expect(testData).toHaveProperty('permissions');
      expect(testData).toHaveProperty('displayNumber');
      expect(testData).toHaveProperty('children');
    });
  });
});
