import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import AddUsersPage from '../../../../src/pages/userPermissions/users/AddUsersPage';

// Mock dependencies
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    state: null
  }),
}));

// Mock JWT Context
jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext({
      user: {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        organisationCategory: 'Chidhagni'
      }
    })
  };
});

// Mock DialogData component
jest.mock('../../../../src/pages/userPermissions/roles/DialogData', () => {
  return function MockDialogData({ siteMapData, onPermissionsChange }) {
    return (
      <div data-testid="dialog-data">
        <div data-testid="sitemap-data">
          {siteMapData && siteMapData.length > 0 ? JSON.stringify(siteMapData) : 'Dashboard'}
        </div>
        <button
          data-testid="permissions-change-button"
          onClick={() => onPermissionsChange && onPermissionsChange({})}
        >
          Change Permissions
        </button>
      </div>
    );
  };
});

// Mock custom components
jest.mock('components/custom-components/CustomDropdownField', () => {
  return function MockCustomDropdownField({ name, placeholder, getOptions, onChange, ...props }) {
    const options = getOptions ? getOptions() : [];
    return (
      <div data-testid={`dropdown-${name}`}>
        <select 
          data-testid={`dropdown-select-${name}`}
          name={name}
          onChange={(e) => onChange && onChange(e.target.value)}
        >
          <option value="">{placeholder}</option>
          {options.map((option, index) => (
            <option key={index} value={option.value || option.key}>
              {option.key || option.label}
            </option>
          ))}
        </select>
      </div>
    );
  };
});

jest.mock('components/custom-components/CustomNameField', () => {
  return function MockCustomNameField({ name, placeholder, ...props }) {
    return (
      <div data-testid={`name-field-${name}`}>
        <input 
          data-testid={`name-input-${name}`}
          name={name}
          placeholder={placeholder}
        />
      </div>
    );
  };
});

jest.mock('components/custom-components/CustomAllCharactersField', () => {
  return function MockCustomAllCharactersField({ name, placeholder, ...props }) {
    return (
      <div data-testid={`all-chars-field-${name}`}>
        <textarea 
          data-testid={`all-chars-input-${name}`}
          name={name}
          placeholder={placeholder}
        />
      </div>
    );
  };
});

jest.mock('components/custom-components/CustomInputLabel', () => {
  return function MockCustomInputLabel({ children, required }) {
    return (
      <label data-testid="custom-input-label">
        {children}
        {required && <span style={{ color: 'red' }}>*</span>}
      </label>
    );
  };
});

jest.mock('components/MainCard', () => {
  return function MockMainCard({ children, ...props }) {
    return <div data-testid="main-card" {...props}>{children}</div>;
  };
});

const theme = createTheme();

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('AddUsersPage Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders without crashing', () => {
      renderWithProviders(<AddUsersPage />);
      expect(screen.getByRole('heading', { name: 'Update User' })).toBeInTheDocument();
    });

    test('renders main card container', () => {
      renderWithProviders(<AddUsersPage />);
      expect(screen.getByTestId('main-card')).toBeInTheDocument();
    });

    test('renders form components', () => {
      renderWithProviders(<AddUsersPage />);

      // Check for form field containers
      expect(screen.getByTestId('name-field-name')).toBeInTheDocument();
      expect(screen.getByTestId('dropdown-tenantId')).toBeInTheDocument();
      expect(screen.getByTestId('all-chars-field-description')).toBeInTheDocument();
    });

    test('renders DialogData component', () => {
      renderWithProviders(<AddUsersPage />);
      expect(screen.getByTestId('dialog-data')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    test('form fields are interactive', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);

      // Test that form fields exist and can be interacted with
      const nameInput = screen.getByTestId('name-input-name');
      const tenantDropdown = screen.getByTestId('dropdown-select-tenantId');
      const descriptionInput = screen.getByTestId('all-chars-input-description');

      expect(nameInput).toBeInTheDocument();
      expect(tenantDropdown).toBeInTheDocument();
      expect(descriptionInput).toBeInTheDocument();

      // Test basic interaction
      await user.type(nameInput, 'Test User');
      expect(nameInput).toHaveValue('Test User');
    });

    test('dropdown fields have options', () => {
      renderWithProviders(<AddUsersPage />);

      const tenantDropdown = screen.getByTestId('dropdown-select-tenantId');

      // Check that dropdown has placeholder options
      expect(tenantDropdown).toBeInTheDocument();
      expect(screen.getByText('Select')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    test('shows validation errors for empty required fields', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);

      const saveButton = screen.getByRole('button', { name: /update user/i });
      await user.click(saveButton);

      // Form validation would be handled by react-hook-form
      expect(saveButton).toBeInTheDocument();
    });

    test('validates user name format', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);

      const nameInput = screen.getByTestId('name-input-name');
      await user.type(nameInput, '123'); // Invalid format

      const saveButton = screen.getByRole('button', { name: /update user/i });
      await user.click(saveButton);

      // Validation would be handled by the CustomNameField component
      expect(nameInput).toHaveValue('123');
    });
  });

  describe('Permissions Management', () => {
    test('handles permissions change from DialogData', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);
      
      const permissionsButton = screen.getByTestId('permissions-change-button');
      await user.click(permissionsButton);
      
      // Should handle permissions change
      expect(permissionsButton).toBeInTheDocument();
    });

    test('displays sitemap data in DialogData', () => {
      renderWithProviders(<AddUsersPage />);
      
      const sitemapData = screen.getByTestId('sitemap-data');
      expect(sitemapData).toBeInTheDocument();
      
      // Should contain mock sitemap data
      const dataContent = sitemapData.textContent;
      expect(dataContent).toContain('Dashboard');
    });
  });

  describe('Navigation', () => {
    test('cancel button navigates back', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);
      
      const cancelButton = screen.getByRole('link', { name: /cancel/i });
      await user.click(cancelButton);
      
      // Navigation would be handled by the useNavigate hook
      expect(cancelButton).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper form labels', () => {
      renderWithProviders(<AddUsersPage />);
      
      const labels = screen.getAllByTestId('custom-input-label');
      expect(labels.length).toBeGreaterThan(0);
    });

    test('indicates required fields', () => {
      renderWithProviders(<AddUsersPage />);
      
      // Check for red asterisks that indicate required fields
      const requiredIndicators = screen.getAllByText('*');
      expect(requiredIndicators.length).toBeGreaterThan(0);
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);
      
      // Tab through the form elements
      await user.tab();
      
      // The first focusable element should be the cancel button
      const cancelButton = screen.getByRole('link', { name: /cancel/i });
      expect(document.activeElement).toBe(cancelButton);
    });
  });

  describe('Error Handling', () => {
    test('displays error messages when validation fails', () => {
      renderWithProviders(<AddUsersPage />);
      
      // Error messages would be displayed by the form components
      // when validation fails
      const nameField = screen.getByTestId('name-field-name');
      expect(nameField).toBeInTheDocument();
    });

    test('handles API errors gracefully', () => {
      renderWithProviders(<AddUsersPage />);

      // API error handling would be implemented in the form submission
      const form = screen.getByTestId('main-card');
      expect(form).toBeInTheDocument();
    });
  });

  describe('Snackbar Notifications', () => {
    test('shows success message on successful save', () => {
      renderWithProviders(<AddUsersPage />);

      // Snackbar would be shown after successful form submission
      const saveButton = screen.getByRole('button', { name: /update user/i });
      expect(saveButton).toBeInTheDocument();
    });

    test('shows error message on save failure', () => {
      renderWithProviders(<AddUsersPage />);

      // Error snackbar would be shown after failed form submission
      const saveButton = screen.getByRole('button', { name: /update user/i });
      expect(saveButton).toBeInTheDocument();
    });
  });

  describe('Organization/Tenant Handling', () => {
    test('loads tenant options based on user organization', () => {
      renderWithProviders(<AddUsersPage />);
      
      const tenantDropdown = screen.getByTestId('dropdown-select-tenantId');
      expect(tenantDropdown).toBeInTheDocument();
    });

    test('updates NGO specific data when tenant changes', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddUsersPage />);

      const tenantDropdown = screen.getByTestId('dropdown-select-tenantId');

      // Simulate tenant selection
      fireEvent.change(tenantDropdown, { target: { value: 'org1' } });

      // The mock dropdown should handle the change
      expect(tenantDropdown).toBeInTheDocument();
    });
  });

  describe('Edit Mode', () => {
    test('populates form fields when editing existing user', () => {
      // Mock location state with edit data
      const mockUseLocation = jest.fn(() => ({
        state: {
          data: {
            name: 'Existing User',
            description: 'Existing Description'
          }
        }
      }));
      
      jest.doMock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useLocation: mockUseLocation,
      }));
      
      renderWithProviders(<AddUsersPage />);
      
      // Form should be populated with existing data
      expect(screen.getByTestId('name-input-name')).toBeInTheDocument();
    });
  });
});
