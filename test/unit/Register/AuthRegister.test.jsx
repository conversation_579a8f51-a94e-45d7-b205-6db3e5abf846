import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('hooks/useAuth');
jest.mock('hooks/useScriptRef');
jest.mock('api/snackbar');
jest.mock('utils/password-strength');

jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext()
  };
});

// Mock utils/axios to prevent import.meta issues
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: {
      common: {}
    }
  }
}));

// Now import the component after mocking dependencies
import AuthRegister from '../../../src/sections/auth/auth-forms/AuthRegister';

// Mock the components
jest.mock('components/@extended/IconButton', () => {
  return function MockIconButton({ children, onClick, ...props }) {
    return (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div>{children}</div>;
  };
});

// Mock the icons
jest.mock('iconsax-react', () => ({
  Eye: () => <span data-testid="eye-icon">Eye</span>,
  EyeSlash: () => <span data-testid="eye-slash-icon">EyeSlash</span>
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Link: ({ children, to, ...props }) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
  useNavigate: () => mockNavigate
}));

describe('AuthRegister Component - Comprehensive Tests', () => {
  let mockRegister;
  let mockScriptRef;
  let mockOpenSnackbar;
  let mockStrengthIndicator;
  let useAuth;
  let useScriptRef;
  let openSnackbar;
  let strengthIndicator;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock functions
    mockRegister = jest.fn();
    mockScriptRef = { current: true };
    mockOpenSnackbar = jest.fn();
    mockStrengthIndicator = jest.fn();

    // Mock the hooks and utilities
    useAuth = require('hooks/useAuth').default;
    useScriptRef = require('hooks/useScriptRef').default;
    openSnackbar = require('api/snackbar').openSnackbar;
    strengthIndicator = require('utils/password-strength').strengthIndicator;

    useAuth.mockReturnValue({
      isLoggedIn: false,
      register: mockRegister
    });

    useScriptRef.mockReturnValue(mockScriptRef);
    openSnackbar.mockImplementation(mockOpenSnackbar);
    strengthIndicator.mockImplementation(mockStrengthIndicator);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders registration form with all required fields', () => {
      render(<AuthRegister />);

      // Check for form title
      expect(screen.getByText('Sign up')).toBeInTheDocument();

      // Check for all input fields
      expect(screen.getByLabelText(/tenant name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/contact person name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/individual email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/website/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/organization email/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();

      // Check for terms and conditions checkbox
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      expect(screen.getByText(/by signing up, you agree to our/i)).toBeInTheDocument();
    });

    test('renders input fields with correct attributes', () => {
      render(<AuthRegister />);

      // Check tenant name input
      const tenantNameInput = screen.getByLabelText(/tenant name/i);
      expect(tenantNameInput).toHaveAttribute('type', 'text');
      expect(tenantNameInput).toHaveAttribute('placeholder', 'Company Name');
      expect(tenantNameInput).toHaveAttribute('name', 'tenantName');

      // Check contact person name input
      const contactPersonInput = screen.getByLabelText(/contact person name/i);
      expect(contactPersonInput).toHaveAttribute('type', 'text');
      expect(contactPersonInput).toHaveAttribute('placeholder', 'John Doe');
      expect(contactPersonInput).toHaveAttribute('name', 'contactPersonName');

      // Check individual email input
      const individualEmailInput = screen.getByLabelText(/individual email/i);
      expect(individualEmailInput).toHaveAttribute('type', 'email');
      expect(individualEmailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(individualEmailInput).toHaveAttribute('name', 'individualEmail');

      // Check mobile number input
      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      expect(mobileNumberInput).toHaveAttribute('type', 'tel');
      expect(mobileNumberInput).toHaveAttribute('placeholder', '9515599022');
      expect(mobileNumberInput).toHaveAttribute('name', 'mobileNumber');

      // Check website input
      const websiteInput = screen.getByLabelText(/website/i);
      expect(websiteInput).toHaveAttribute('type', 'url');
      expect(websiteInput).toHaveAttribute('placeholder', 'https://www.company.com');
      expect(websiteInput).toHaveAttribute('name', 'website');

      // Check organization email input
      const orgEmailInput = screen.getByLabelText(/organization email/i);
      expect(orgEmailInput).toHaveAttribute('type', 'email');
      expect(orgEmailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(orgEmailInput).toHaveAttribute('name', 'orgEmail');
    });

    test('renders "Already have an account?" link', () => {
      render(<AuthRegister />);

      expect(screen.getByText(/already have an account/i)).toBeInTheDocument();
    });

    test('renders terms and privacy policy links', () => {
      render(<AuthRegister />);

      expect(screen.getByText(/terms of service/i)).toBeInTheDocument();
      expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();
    });
  });

  describe('User Input Handling', () => {
    test('handles tenant name input changes', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const tenantNameInput = screen.getByLabelText(/tenant name/i);
      await user.type(tenantNameInput, 'Test Company');

      expect(tenantNameInput).toHaveValue('Test Company');
    });

    test('handles contact person name input changes', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const contactPersonInput = screen.getByLabelText(/contact person name/i);
      await user.type(contactPersonInput, 'John Doe');

      expect(contactPersonInput).toHaveValue('JohnDoe'); // Spaces are prevented
    });

    test('handles individual email input changes', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const individualEmailInput = screen.getByLabelText(/individual email/i);
      await user.type(individualEmailInput, '<EMAIL>');

      expect(individualEmailInput).toHaveValue('<EMAIL>');
    });

    test('handles mobile number input changes with numeric restriction', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      await user.type(mobileNumberInput, '**********');

      expect(mobileNumberInput).toHaveValue('**********');
    });

    test('handles website input changes', async () => {
      const user = userEvent.setup({ delay: null });
      render(<AuthRegister />);

      const websiteInput = screen.getByLabelText(/website/i);
      await user.clear(websiteInput);
      await user.type(websiteInput, 'https://www.example.com');

      expect(websiteInput).toHaveValue('https://www.example.com');
    }, 15000);

    test('handles organization email input changes', async () => {
      const user = userEvent.setup({ delay: null });
      render(<AuthRegister />);

      const orgEmailInput = screen.getByLabelText(/organization email/i);
      await user.clear(orgEmailInput);
      await user.type(orgEmailInput, '<EMAIL>');

      expect(orgEmailInput).toHaveValue('<EMAIL>');
    }, 15000);

    test('handles checkbox state changes', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeChecked(); // Initially checked

      await user.click(checkbox);
      expect(checkbox).not.toBeChecked();

      await user.click(checkbox);
      expect(checkbox).toBeChecked();
    });
  });

  describe('Mobile Number Input Validation', () => {
    test('restricts mobile number input to numbers only', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      
      // Try to type letters and special characters
      await user.type(mobileNumberInput, 'abc123def456!@#');
      
      // Should only contain numbers
      expect(mobileNumberInput).toHaveValue('123456');
    });

    test('limits mobile number to 10 digits', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      
      // Try to type more than 10 digits
      await user.type(mobileNumberInput, '**********1234');
      
      // Should be limited to 10 digits
      expect(mobileNumberInput).toHaveValue('**********');
    });
  });

  describe('Contact Person Name Input Validation', () => {
    test('prevents numeric input in contact person name', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const contactPersonInput = screen.getByLabelText(/contact person name/i);

      // Try to type numbers
      await user.type(contactPersonInput, 'John123Doe456');

      // Numbers should be prevented, only letters should remain
      expect(contactPersonInput).toHaveValue('JohnDoe');
    });

    test('prevents spaces in contact person name', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const contactPersonInput = screen.getByLabelText(/contact person name/i);

      // Try to type with spaces
      await user.type(contactPersonInput, 'John Doe');

      // Spaces should be prevented
      expect(contactPersonInput).toHaveValue('JohnDoe');
    });
  });

  describe('Form Validation', () => {
    test('shows validation errors for empty required fields', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/tenant name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/contact person name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/individual email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/mobile number is required/i)).toBeInTheDocument();
        expect(screen.getByText(/website is required/i)).toBeInTheDocument();
        expect(screen.getByText(/organization email is required/i)).toBeInTheDocument();
      });
    });

    test('validates email format for individual email', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const individualEmailInput = screen.getByLabelText(/individual email/i);
      await user.type(individualEmailInput, 'invalid-email');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });
    });

    test('validates email format for organization email', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const orgEmailInput = screen.getByLabelText(/organization email/i);
      await user.type(orgEmailInput, 'invalid-email');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      });
    });

    test('validates mobile number format', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      await user.type(mobileNumberInput, '123'); // Less than 10 digits
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/mobile number must be exactly 10 digits/i)).toBeInTheDocument();
      });
    });

    test('validates website URL format', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const websiteInput = screen.getByLabelText(/website/i);
      await user.type(websiteInput, 'invalid-url');
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/must be a valid url/i)).toBeInTheDocument();
      });
    });

    test('form submission is prevented when password validation fails', async () => {
      const user = userEvent.setup({ delay: null });
      render(<AuthRegister />);

      const submitButton = screen.getByRole('button', { name: /create account/i });

      // Fill all visible required fields
      await user.type(screen.getByLabelText(/tenant name/i), 'Test Company');
      await user.type(screen.getByLabelText(/contact person name/i), 'John Doe');
      await user.type(screen.getByLabelText(/individual email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/website/i), 'https://example.com');
      await user.type(screen.getByLabelText(/organization email/i), '<EMAIL>');

      await user.click(submitButton);

      // The form should not submit because password is missing (validation should fail)
      await waitFor(() => {
        expect(mockRegister).not.toHaveBeenCalled();
      }, { timeout: 5000 });
    }, 30000);
  });

  describe('Form Submission', () => {
    test('form submission is prevented due to missing password field', async () => {
      const user = userEvent.setup({ delay: null });
      mockRegister.mockResolvedValue({});

      render(<AuthRegister />);

      // Fill all visible required fields
      await user.type(screen.getByLabelText(/tenant name/i), 'Test Company');
      await user.type(screen.getByLabelText(/contact person name/i), 'John Doe');
      await user.type(screen.getByLabelText(/individual email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/website/i), 'https://example.com');
      await user.type(screen.getByLabelText(/organization email/i), '<EMAIL>');

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      // The form should not submit because password validation will fail
      await waitFor(() => {
        expect(mockRegister).not.toHaveBeenCalled();
      }, { timeout: 15000 });
    }, 20000);

    test('form validation prevents submission when required fields are missing', async () => {
      const user = userEvent.setup();
      mockRegister.mockResolvedValue({});

      render(<AuthRegister />);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      // The form should not submit and should show validation errors
      await waitFor(() => {
        expect(mockRegister).not.toHaveBeenCalled();
        expect(screen.getByText(/tenant name is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    test('handles form validation errors correctly', async () => {
      const user = userEvent.setup();

      render(<AuthRegister />);

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      // Verify that validation errors are shown
      await waitFor(() => {
        expect(screen.getByText(/tenant name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/contact person name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/individual email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/mobile number is required/i)).toBeInTheDocument();
        expect(screen.getByText(/website is required/i)).toBeInTheDocument();
        expect(screen.getByText(/organization email is required/i)).toBeInTheDocument();
      });
    });

    test('handles email validation errors', async () => {
      const user = userEvent.setup({ delay: null });

      render(<AuthRegister />);

      // Enter invalid email
      const individualEmailInput = screen.getByLabelText(/individual email/i);
      await user.clear(individualEmailInput);
      await user.type(individualEmailInput, 'invalid-email');

      // Try to submit the form to trigger validation
      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/must be a valid email/i)).toBeInTheDocument();
      }, { timeout: 5000 });
    }, 15000);

    test('handles mobile number validation errors', async () => {
      const user = userEvent.setup();

      render(<AuthRegister />);

      // Enter invalid mobile number
      const mobileNumberInput = screen.getByLabelText(/mobile number/i);
      await user.type(mobileNumberInput, '123'); // Less than 10 digits
      await user.tab(); // Trigger blur event

      await waitFor(() => {
        expect(screen.getByText(/mobile number must be exactly 10 digits/i)).toBeInTheDocument();
      });
    });
  });

  describe('Button State Management', () => {
    test('disables submit button when checkbox is unchecked', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const checkbox = screen.getByRole('checkbox');
      const submitButton = screen.getByRole('button', { name: /create account/i });

      // Initially checkbox is checked and button is enabled
      expect(checkbox).toBeChecked();
      expect(submitButton).not.toBeDisabled();

      // Uncheck the checkbox
      await user.click(checkbox);
      expect(checkbox).not.toBeChecked();
      expect(submitButton).toBeDisabled();

      // Check the checkbox again
      await user.click(checkbox);
      expect(checkbox).toBeChecked();
      expect(submitButton).not.toBeDisabled();
    });

    test('submit button is enabled when checkbox is checked', () => {
      render(<AuthRegister />);

      const checkbox = screen.getByRole('checkbox');
      const submitButton = screen.getByRole('button', { name: /create account/i });

      // Initially checkbox is checked and button is enabled
      expect(checkbox).toBeChecked();
      expect(submitButton).not.toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    test('has proper form labels and ARIA attributes', () => {
      render(<AuthRegister />);

      // Check that all inputs have proper labels
      expect(screen.getByLabelText(/tenant name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/contact person name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/individual email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/website/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/organization email/i)).toBeInTheDocument();

      // Check that form element exists (it may not have role="form" explicitly)
      const formElement = document.querySelector('form');
      expect(formElement).toBeInTheDocument();
      expect(formElement).toHaveAttribute('noValidate');
    });

    test('has proper button labels and roles', () => {
      render(<AuthRegister />);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      expect(submitButton).toHaveAttribute('type', 'submit');

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeInTheDocument();
    });

    test('provides proper error messages for accessibility', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        // Check that error messages are displayed for accessibility
        expect(screen.getByText(/tenant name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/contact person name is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Input Field Behavior', () => {
    test('handles tenant name input correctly', async () => {
      const user = userEvent.setup({ delay: null });
      render(<AuthRegister />);

      const tenantNameInput = screen.getByLabelText(/tenant name/i);
      await user.clear(tenantNameInput);
      await user.type(tenantNameInput, 'Test Company');

      expect(tenantNameInput).toHaveValue('Test Company');
    }, 15000);

    test('handles contact person name input correctly', async () => {
      const user = userEvent.setup();
      render(<AuthRegister />);

      const contactPersonInput = screen.getByLabelText(/contact person name/i);
      await user.type(contactPersonInput, 'John');
      await user.type(contactPersonInput, 'Doe');

      expect(contactPersonInput).toHaveValue('JohnDoe');
    });
  });

  describe('Password Strength Integration', () => {
    test('calls password strength indicator on component mount', () => {
      render(<AuthRegister />);

      expect(mockStrengthIndicator).toHaveBeenCalledWith('');
    });
  });

  describe('Props and Context Integration', () => {
    test('works with different auth context states', () => {
      const mockAuthContext = {
        isLoggedIn: true,
        register: mockRegister
      };

      useAuth.mockReturnValue(mockAuthContext);

      render(<AuthRegister />);

      // Component should render properly with different auth states
      expect(screen.getByText('Sign up')).toBeInTheDocument();
    });

    test('handles navigation link correctly when logged in', () => {
      const mockAuthContext = {
        isLoggedIn: true,
        register: mockRegister
      };

      useAuth.mockReturnValue(mockAuthContext);

      render(<AuthRegister />);

      const loginLink = screen.getByText(/already have an account/i);
      expect(loginLink).toHaveAttribute('href', '/auth/login');
    });

    test('handles navigation link correctly when not logged in', () => {
      const mockAuthContext = {
        isLoggedIn: false,
        register: mockRegister
      };

      useAuth.mockReturnValue(mockAuthContext);

      render(<AuthRegister />);

      const loginLink = screen.getByText(/already have an account/i);
      expect(loginLink).toHaveAttribute('href', '/login');
    });
  });
});
