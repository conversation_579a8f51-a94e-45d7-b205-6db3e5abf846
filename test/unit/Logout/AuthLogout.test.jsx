import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext()
  };
});

// Mock utils/axios to prevent import.meta issues
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: {
      common: {}
    }
  }
}));

// Now import the component after mocking dependencies
import AuthLogout from '../../../src/sections/auth/auth-forms/AuthLogout';

// Mock the components
jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div data-testid="animate-button">{children}</div>;
  };
});

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  Link: ({ children, to, ...props }) => (
    <a href={to} {...props}>
      {children}
    </a>
  )
}));

describe('AuthLogout Component - Comprehensive Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders logout message and navigation buttons', () => {
      render(<AuthLogout />);

      // Check for logout message
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
      expect(screen.getByText(/successfully logged out 👋🏻/i)).toBeInTheDocument();

      // Check for login button
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();

      // Check for create account button
      expect(screen.getByRole('link', { name: /create new account/i })).toBeInTheDocument();
    });

    test('renders buttons with correct attributes', () => {
      render(<AuthLogout />);

      // Check login button
      const loginButton = screen.getByRole('link', { name: /login/i });
      expect(loginButton).toHaveAttribute('href', '/login');

      // Check create account button
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });
      expect(createAccountButton).toHaveAttribute('href', '/register');
    });

    test('renders with proper structure and styling', () => {
      render(<AuthLogout />);

      // Check that the logout message has proper styling
      const logoutMessage = screen.getByText(/successfully logged out/i);
      expect(logoutMessage).toBeInTheDocument();

      // Check that buttons are rendered
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /create new account/i })).toBeInTheDocument();
    });

    test('renders AnimateButton wrapper', () => {
      render(<AuthLogout />);

      // Check that AnimateButton is rendered
      expect(screen.getByTestId('animate-button')).toBeInTheDocument();
    });
  });

  describe('User Interaction Handling', () => {
    test('login button is clickable and navigates correctly', async () => {
      const user = userEvent.setup();
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      
      // Button should be clickable
      expect(loginButton).not.toBeDisabled();
      
      // Check href attribute
      expect(loginButton).toHaveAttribute('href', '/login');
    });

    test('create account button is clickable and navigates correctly', async () => {
      const user = userEvent.setup();
      render(<AuthLogout />);

      const createAccountButton = screen.getByRole('link', { name: /create new account/i });
      
      // Button should be clickable
      expect(createAccountButton).not.toBeDisabled();
      
      // Check href attribute
      expect(createAccountButton).toHaveAttribute('href', '/register');
    });

    test('handles button clicks without errors', async () => {
      const user = userEvent.setup();
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });

      // Should not throw errors when clicked
      await user.click(loginButton);
      await user.click(createAccountButton);

      // Component should still be rendered
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
    });
  });

  describe('Navigation Behavior', () => {
    test('login button has correct navigation properties', () => {
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      expect(loginButton).toHaveAttribute('href', '/login');
    });

    test('create account button has correct navigation properties', () => {
      render(<AuthLogout />);

      const createAccountButton = screen.getByRole('link', { name: /create new account/i });
      expect(createAccountButton).toHaveAttribute('href', '/register');
    });

    test('buttons use RouterLink component for navigation', () => {
      render(<AuthLogout />);

      // Both buttons should be rendered as links
      const loginButton = screen.getByRole('link', { name: /login/i });
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });

      expect(loginButton).toBeInTheDocument();
      expect(createAccountButton).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper button labels and roles', () => {
      render(<AuthLogout />);

      // Check that buttons have proper roles
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /create new account/i })).toBeInTheDocument();
    });

    test('has proper text content for screen readers', () => {
      render(<AuthLogout />);

      // Check that text content is accessible
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
      expect(screen.getByText(/login/i)).toBeInTheDocument();
      expect(screen.getByText(/create new account/i)).toBeInTheDocument();
    });

    test('buttons have proper semantic structure', () => {
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });

      // Buttons should be properly structured for accessibility
      expect(loginButton.tagName).toBe('A');
      expect(createAccountButton.tagName).toBe('A');
    });
  });

  describe('Component Structure', () => {
    test('renders with correct layout structure', () => {
      render(<AuthLogout />);

      // Check that all main elements are present
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /create new account/i })).toBeInTheDocument();
    });

    test('renders logout message with emoji', () => {
      render(<AuthLogout />);

      // Check that the emoji is rendered as part of the message
      expect(screen.getByText(/successfully logged out 👋🏻/i)).toBeInTheDocument();
    });

    test('renders buttons in correct order', () => {
      render(<AuthLogout />);

      const buttons = screen.getAllByRole('link');
      
      // Should have exactly 2 buttons
      expect(buttons).toHaveLength(2);
      
      // Login button should come first
      expect(buttons[0]).toHaveTextContent(/login/i);
      
      // Create account button should come second
      expect(buttons[1]).toHaveTextContent(/create new account/i);
    });
  });

  describe('Props and Context Integration', () => {
    test('works without requiring props', () => {
      // Component should render without any props
      expect(() => render(<AuthLogout />)).not.toThrow();
      
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
    });

    test('integrates with navigation context', () => {
      render(<AuthLogout />);

      // Component should use useNavigate hook (mocked)
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
    });

    test('handles router link integration correctly', () => {
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });

      // Both should be proper router links
      expect(loginButton).toHaveAttribute('href', '/login');
      expect(createAccountButton).toHaveAttribute('href', '/register');
    });
  });

  describe('Button Styling and Variants', () => {
    test('login button has correct styling properties', () => {
      render(<AuthLogout />);

      const loginButton = screen.getByRole('link', { name: /login/i });
      expect(loginButton).toBeInTheDocument();
      
      // Button should be rendered as a link
      expect(loginButton.tagName).toBe('A');
    });

    test('create account button has correct styling properties', () => {
      render(<AuthLogout />);

      const createAccountButton = screen.getByRole('link', { name: /create new account/i });
      expect(createAccountButton).toBeInTheDocument();
      
      // Button should be rendered as a link
      expect(createAccountButton.tagName).toBe('A');
    });
  });

  describe('Error Handling', () => {
    test('renders without errors when navigation is unavailable', () => {
      // Component should still render even if navigation fails
      expect(() => render(<AuthLogout />)).not.toThrow();
      
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
    });

    test('handles missing router context gracefully', () => {
      // Component should render without throwing errors
      render(<AuthLogout />);
      
      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /create new account/i })).toBeInTheDocument();
    });
  });

  describe('UI Elements and Content', () => {
    test('displays correct logout message text', () => {
      render(<AuthLogout />);

      expect(screen.getByText(/successfully logged out/i)).toBeInTheDocument();
    });

    test('displays correct button text', () => {
      render(<AuthLogout />);

      expect(screen.getByText(/^login$/i)).toBeInTheDocument();
      expect(screen.getByText(/create new account/i)).toBeInTheDocument();
    });

    test('renders all UI elements in correct hierarchy', () => {
      render(<AuthLogout />);

      // Check that all elements are present and accessible
      const logoutMessage = screen.getByText(/successfully logged out/i);
      const loginButton = screen.getByRole('link', { name: /login/i });
      const createAccountButton = screen.getByRole('link', { name: /create new account/i });

      expect(logoutMessage).toBeInTheDocument();
      expect(loginButton).toBeInTheDocument();
      expect(createAccountButton).toBeInTheDocument();
    });
  });
});
