const { fuzzyFilter, fuzzySort } = require('../../../src/utils/tableUtils');

// Mock the external dependencies
jest.mock('@tanstack/match-sorter-utils', () => ({
  rankItem: jest.fn(),
  compareItems: jest.fn()
}));

jest.mock('@tanstack/table-core', () => ({
  sortingFns: {
    alphanumeric: jest.fn()
  }
}));

describe('tableUtils', () => {
  let mockRankItem;
  let mockCompareItems;
  let mockAlphanumeric;

  beforeEach(() => {
    // Get the mocked functions
    mockRankItem = require('@tanstack/match-sorter-utils').rankItem;
    mockCompareItems = require('@tanstack/match-sorter-utils').compareItems;
    mockAlphanumeric = require('@tanstack/table-core').sortingFns.alphanumeric;

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('fuzzyFilter', () => {
    let mockRow;
    let mockAddMeta;

    beforeEach(() => {
      mockRow = {
        getValue: jest.fn()
      };
      mockAddMeta = jest.fn();
    });

    test('calls rankItem with correct parameters and returns passed status', () => {
      const mockItemRank = { passed: true, rankedValue: 'test' };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue('Test Value');

      const result = fuzzyFilter(mockRow, 'testColumn', 'test', mockAddMeta);

      expect(mockRow.getValue).toHaveBeenCalledWith('testColumn');
      expect(mockRankItem).toHaveBeenCalledWith('Test Value', 'test');
      expect(mockAddMeta).toHaveBeenCalledWith(mockItemRank);
      expect(result).toBe(true);
    });

    test('returns false when item does not pass ranking', () => {
      const mockItemRank = { passed: false, rankedValue: 'test' };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue('Different Value');

      const result = fuzzyFilter(mockRow, 'testColumn', 'search', mockAddMeta);

      expect(result).toBe(false);
    });

    test('handles empty search value', () => {
      const mockItemRank = { passed: true, rankedValue: '' };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue('Any Value');

      const result = fuzzyFilter(mockRow, 'testColumn', '', mockAddMeta);

      expect(mockRankItem).toHaveBeenCalledWith('Any Value', '');
      expect(result).toBe(true);
    });

    test('handles null row value', () => {
      const mockItemRank = { passed: false, rankedValue: null };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue(null);

      const result = fuzzyFilter(mockRow, 'testColumn', 'search', mockAddMeta);

      expect(mockRankItem).toHaveBeenCalledWith(null, 'search');
      expect(result).toBe(false);
    });

    test('handles undefined row value', () => {
      const mockItemRank = { passed: false, rankedValue: undefined };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue(undefined);

      const result = fuzzyFilter(mockRow, 'testColumn', 'search', mockAddMeta);

      expect(mockRankItem).toHaveBeenCalledWith(undefined, 'search');
      expect(result).toBe(false);
    });

    test('works with different column IDs', () => {
      const mockItemRank = { passed: true, rankedValue: 'test' };
      mockRankItem.mockReturnValue(mockItemRank);
      mockRow.getValue.mockReturnValue('Column Value');

      fuzzyFilter(mockRow, 'firstName', 'john', mockAddMeta);
      expect(mockRow.getValue).toHaveBeenCalledWith('firstName');

      fuzzyFilter(mockRow, 'lastName', 'doe', mockAddMeta);
      expect(mockRow.getValue).toHaveBeenCalledWith('lastName');

      fuzzyFilter(mockRow, 'email', 'example', mockAddMeta);
      expect(mockRow.getValue).toHaveBeenCalledWith('email');
    });
  });

  describe('fuzzySort', () => {
    let mockRowA;
    let mockRowB;

    beforeEach(() => {
      mockRowA = {
        columnFiltersMeta: {}
      };
      mockRowB = {
        columnFiltersMeta: {}
      };
    });

    test('returns compareItems result when both rows have filter meta', () => {
      const mockMetaA = { rank: 1 };
      const mockMetaB = { rank: 2 };
      
      mockRowA.columnFiltersMeta['testColumn'] = mockMetaA;
      mockRowB.columnFiltersMeta['testColumn'] = mockMetaB;
      
      mockCompareItems.mockReturnValue(-1);

      const result = fuzzySort(mockRowA, mockRowB, 'testColumn');

      expect(mockCompareItems).toHaveBeenCalledWith(mockMetaA, mockMetaB);
      expect(result).toBe(-1);
    });

    test('falls back to alphanumeric sort when compareItems returns 0', () => {
      const mockMetaA = { rank: 1 };
      const mockMetaB = { rank: 1 };
      
      mockRowA.columnFiltersMeta['testColumn'] = mockMetaA;
      mockRowB.columnFiltersMeta['testColumn'] = mockMetaB;
      
      mockCompareItems.mockReturnValue(0);
      mockAlphanumeric.mockReturnValue(1);

      const result = fuzzySort(mockRowA, mockRowB, 'testColumn');

      expect(mockCompareItems).toHaveBeenCalledWith(mockMetaA, mockMetaB);
      expect(mockAlphanumeric).toHaveBeenCalledWith(mockRowA, mockRowB, 'testColumn');
      expect(result).toBe(1);
    });

    test('falls back to alphanumeric sort when rowA has no filter meta', () => {
      mockRowB.columnFiltersMeta['testColumn'] = { rank: 1 };
      
      mockAlphanumeric.mockReturnValue(-1);

      const result = fuzzySort(mockRowA, mockRowB, 'testColumn');

      expect(mockCompareItems).not.toHaveBeenCalled();
      expect(mockAlphanumeric).toHaveBeenCalledWith(mockRowA, mockRowB, 'testColumn');
      expect(result).toBe(-1);
    });

    test('falls back to alphanumeric sort when rowB has no filter meta', () => {
      mockRowA.columnFiltersMeta['testColumn'] = { rank: 1 };
      // Ensure rowB doesn't have the filter meta for this column
      delete mockRowB.columnFiltersMeta['testColumn'];

      mockCompareItems.mockReturnValue(0); // This should be called and return 0
      mockAlphanumeric.mockReturnValue(1);

      const result = fuzzySort(mockRowA, mockRowB, 'testColumn');

      // The function will call compareItems because rowA has filter meta
      expect(mockCompareItems).toHaveBeenCalledWith({ rank: 1 }, undefined);
      expect(mockAlphanumeric).toHaveBeenCalledWith(mockRowA, mockRowB, 'testColumn');
      expect(result).toBe(1);
    });

    test('falls back to alphanumeric sort when neither row has filter meta', () => {
      mockAlphanumeric.mockReturnValue(0);

      const result = fuzzySort(mockRowA, mockRowB, 'testColumn');

      expect(mockCompareItems).not.toHaveBeenCalled();
      expect(mockAlphanumeric).toHaveBeenCalledWith(mockRowA, mockRowB, 'testColumn');
      expect(result).toBe(0);
    });

    test('handles different column IDs', () => {
      mockRowA.columnFiltersMeta['firstName'] = { rank: 1 };
      mockRowB.columnFiltersMeta['firstName'] = { rank: 2 };
      
      mockCompareItems.mockReturnValue(1);

      const result = fuzzySort(mockRowA, mockRowB, 'firstName');

      expect(mockCompareItems).toHaveBeenCalledWith(
        mockRowA.columnFiltersMeta['firstName'],
        mockRowB.columnFiltersMeta['firstName']
      );
      expect(result).toBe(1);
    });

    test('handles missing columnFiltersMeta property', () => {
      delete mockRowA.columnFiltersMeta;
      delete mockRowB.columnFiltersMeta;

      mockAlphanumeric.mockReturnValue(0);

      // This will throw because the function tries to access columnFiltersMeta[columnId]
      expect(() => fuzzySort(mockRowA, mockRowB, 'testColumn')).toThrow();
    });
  });

  describe('integration tests', () => {
    test('fuzzyFilter and fuzzySort work together', () => {
      // Setup for fuzzyFilter
      const mockRow = { getValue: jest.fn().mockReturnValue('Test Value') };
      const mockAddMeta = jest.fn();
      const mockItemRank = { passed: true, rank: 1 };
      mockRankItem.mockReturnValue(mockItemRank);

      // Test fuzzyFilter
      const filterResult = fuzzyFilter(mockRow, 'testColumn', 'test', mockAddMeta);
      expect(filterResult).toBe(true);
      expect(mockAddMeta).toHaveBeenCalledWith(mockItemRank);

      // Setup for fuzzySort using the meta from fuzzyFilter
      const rowA = { columnFiltersMeta: { testColumn: mockItemRank } };
      const rowB = { columnFiltersMeta: { testColumn: { rank: 2 } } };
      
      mockCompareItems.mockReturnValue(-1);

      // Test fuzzySort
      const sortResult = fuzzySort(rowA, rowB, 'testColumn');
      expect(sortResult).toBe(-1);
    });
  });
});
