const getDropzoneData = require('../../../src/utils/getDropzoneData').default;

describe('getDropzoneData utility', () => {
  describe('string input', () => {
    test('handles string without index', () => {
      const result = getDropzoneData('test-file.jpg');
      
      expect(result).toEqual({
        key: 'test-file.jpg',
        preview: 'test-file.jpg'
      });
    });

    test('handles string with index', () => {
      const result = getDropzoneData('test-file.jpg', 1);
      
      expect(result).toEqual({
        key: 'test-file.jpg-1',
        preview: 'test-file.jpg'
      });
    });

    test('handles empty string', () => {
      const result = getDropzoneData('');
      
      expect(result).toEqual({
        key: '',
        preview: ''
      });
    });

    test('handles string with special characters', () => {
      const result = getDropzoneData('test file (1).jpg', 2);
      
      expect(result).toEqual({
        key: 'test file (1).jpg-2',
        preview: 'test file (1).jpg'
      });
    });
  });

  describe('file object input', () => {
    let mockFile;

    beforeEach(() => {
      mockFile = {
        name: 'test-document.pdf',
        size: 1024000,
        path: '/uploads/test-document.pdf',
        type: 'application/pdf',
        preview: 'blob:http://localhost:3000/test-preview',
        lastModified: 1640995200000,
        lastModifiedDate: new Date('2022-01-01T00:00:00.000Z')
      };
    });

    test('handles file object without index', () => {
      const result = getDropzoneData(mockFile);
      
      expect(result).toEqual({
        key: 'test-document.pdf',
        name: 'test-document.pdf',
        size: 1024000,
        path: '/uploads/test-document.pdf',
        type: 'application/pdf',
        preview: 'blob:http://localhost:3000/test-preview',
        lastModified: 1640995200000,
        lastModifiedDate: new Date('2022-01-01T00:00:00.000Z')
      });
    });

    test('handles file object with index', () => {
      const result = getDropzoneData(mockFile, 3);
      
      expect(result).toEqual({
        key: 'test-document.pdf-3',
        name: 'test-document.pdf',
        size: 1024000,
        path: '/uploads/test-document.pdf',
        type: 'application/pdf',
        preview: 'blob:http://localhost:3000/test-preview',
        lastModified: 1640995200000,
        lastModifiedDate: new Date('2022-01-01T00:00:00.000Z')
      });
    });

    test('handles file object with missing optional properties', () => {
      const minimalFile = {
        name: 'minimal.txt',
        size: 100,
        type: 'text/plain'
      };

      const result = getDropzoneData(minimalFile);
      
      expect(result).toEqual({
        key: 'minimal.txt',
        name: 'minimal.txt',
        size: 100,
        path: undefined,
        type: 'text/plain',
        preview: undefined,
        lastModified: undefined,
        lastModifiedDate: undefined
      });
    });

    test('handles file object with zero index', () => {
      const result = getDropzoneData(mockFile, 0);

      expect(result).toEqual({
        key: 'test-document.pdf', // When index is 0 (falsy), it returns just the filename
        name: 'test-document.pdf',
        size: 1024000,
        path: '/uploads/test-document.pdf',
        type: 'application/pdf',
        preview: 'blob:http://localhost:3000/test-preview',
        lastModified: 1640995200000,
        lastModifiedDate: new Date('2022-01-01T00:00:00.000Z')
      });
    });
  });

  describe('edge cases', () => {
    test('handles null input', () => {
      // The function doesn't handle null gracefully, it will throw
      expect(() => getDropzoneData(null)).toThrow('Cannot read properties of null');
    });

    test('handles undefined input', () => {
      // The function doesn't handle undefined gracefully, it will throw
      expect(() => getDropzoneData(undefined)).toThrow('Cannot read properties of undefined');
    });

    test('handles file with empty name', () => {
      const fileWithEmptyName = {
        name: '',
        size: 0,
        type: 'text/plain'
      };

      const result = getDropzoneData(fileWithEmptyName, 1);
      
      expect(result).toEqual({
        key: '-1',
        name: '',
        size: 0,
        path: undefined,
        type: 'text/plain',
        preview: undefined,
        lastModified: undefined,
        lastModifiedDate: undefined
      });
    });

    test('handles large file sizes', () => {
      const largeFile = {
        name: 'large-file.zip',
        size: 5368709120, // 5GB
        type: 'application/zip'
      };

      const result = getDropzoneData(largeFile);
      
      expect(result.size).toBe(5368709120);
      expect(result.key).toBe('large-file.zip');
    });
  });

  describe('different file types', () => {
    test('handles image files', () => {
      const imageFile = {
        name: 'photo.jpg',
        size: 2048000,
        type: 'image/jpeg',
        preview: 'blob:http://localhost:3000/image-preview'
      };

      const result = getDropzoneData(imageFile);
      
      expect(result.type).toBe('image/jpeg');
      expect(result.preview).toBe('blob:http://localhost:3000/image-preview');
    });

    test('handles video files', () => {
      const videoFile = {
        name: 'video.mp4',
        size: 10485760,
        type: 'video/mp4'
      };

      const result = getDropzoneData(videoFile);
      
      expect(result.type).toBe('video/mp4');
      expect(result.name).toBe('video.mp4');
    });

    test('handles document files', () => {
      const docFile = {
        name: 'document.docx',
        size: 512000,
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      };

      const result = getDropzoneData(docFile);
      
      expect(result.type).toBe('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      expect(result.name).toBe('document.docx');
    });
  });
});
