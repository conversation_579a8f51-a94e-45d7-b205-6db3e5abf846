const { strengthColor, strengthIndicator } = require('../../../src/utils/password-strength');

describe('password-strength utilities', () => {
  describe('strengthColor', () => {
    test('returns Poor/error.main for count < 2', () => {
      expect(strengthColor(0)).toEqual({ label: 'Poor', color: 'error.main' });
      expect(strengthColor(1)).toEqual({ label: 'Poor', color: 'error.main' });
    });

    test('returns Weak/warning.main for count < 3', () => {
      expect(strengthColor(2)).toEqual({ label: 'Weak', color: 'warning.main' });
    });

    test('returns Normal/warning.dark for count < 4', () => {
      expect(strengthColor(3)).toEqual({ label: 'Normal', color: 'warning.dark' });
    });

    test('returns Good/success.main for count < 5', () => {
      expect(strengthColor(4)).toEqual({ label: 'Good', color: 'success.main' });
    });

    test('returns Strong/success.dark for count < 6', () => {
      expect(strengthColor(5)).toEqual({ label: 'Strong', color: 'success.dark' });
    });

    test('returns Poor/error.main for count >= 6', () => {
      expect(strengthColor(6)).toEqual({ label: 'Poor', color: 'error.main' });
      expect(strengthColor(10)).toEqual({ label: 'Poor', color: 'error.main' });
    });
  });

  describe('strengthIndicator', () => {
    test('returns 0 for empty password', () => {
      expect(strengthIndicator('')).toBe(0);
    });

    test('returns 1 for password with only length > 5', () => {
      expect(strengthIndicator('abcdef')).toBe(1);
    });

    test('returns 2 for password with length > 7', () => {
      expect(strengthIndicator('abcdefgh')).toBe(2);
    });

    test('returns 3 for password with length > 7 and numbers', () => {
      expect(strengthIndicator('abcdefgh1')).toBe(3);
    });

    test('returns 4 for password with length > 7, numbers, and special chars', () => {
      expect(strengthIndicator('abcdefgh1!')).toBe(4);
    });

    test('returns 5 for password with all criteria (length > 7, numbers, special chars, mixed case)', () => {
      expect(strengthIndicator('Abcdefgh1!')).toBe(5);
    });

    test('handles various special characters', () => {
      expect(strengthIndicator('Abcdefgh1#')).toBe(5);
      expect(strengthIndicator('Abcdefgh1@')).toBe(5);
      expect(strengthIndicator('Abcdefgh1$')).toBe(5);
      expect(strengthIndicator('Abcdefgh1%')).toBe(5);
      expect(strengthIndicator('Abcdefgh1^')).toBe(5);
      expect(strengthIndicator('Abcdefgh1&')).toBe(5);
      expect(strengthIndicator('Abcdefgh1*')).toBe(5);
      expect(strengthIndicator('Abcdefgh1(')).toBe(5);
      expect(strengthIndicator('Abcdefgh1)')).toBe(5);
      expect(strengthIndicator('Abcdefgh1+')).toBe(5);
      expect(strengthIndicator('Abcdefgh1=')).toBe(5);
      expect(strengthIndicator('Abcdefgh1.')).toBe(5);
      expect(strengthIndicator('Abcdefgh1_')).toBe(5);
      expect(strengthIndicator('Abcdefgh1-')).toBe(5);
    });

    test('handles edge cases', () => {
      // Short password with all other criteria
      expect(strengthIndicator('Aa1!')).toBe(3); // Gets points for number, special chars, and mixed case

      // Long password without other criteria
      expect(strengthIndicator('abcdefghijklmnop')).toBe(2); // Gets points for length > 5 and > 7
      
      // Password with numbers but no other criteria
      expect(strengthIndicator('123456')).toBe(2); // Gets points for length > 5 and numbers
    });

    test('correctly identifies mixed case', () => {
      expect(strengthIndicator('ABCDEFGHijk123!')).toBe(5); // All criteria met
      expect(strengthIndicator('abcdefghijk123!')).toBe(4); // No uppercase
      expect(strengthIndicator('ABCDEFGHIJK123!')).toBe(4); // No lowercase
    });
  });
});
