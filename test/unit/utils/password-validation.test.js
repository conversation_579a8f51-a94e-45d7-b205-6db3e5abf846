const {
  isNumber,
  isLowercase<PERSON><PERSON>,
  isUppercase<PERSON>har,
  isSpecialChar,
  minLength
} = require('../../../src/utils/password-validation');

describe('password-validation utilities', () => {
  describe('isNumber', () => {
    test('returns true for strings containing numbers', () => {
      expect(isNumber('abc123')).toBe(true);
      expect(isNumber('123abc')).toBe(true);
      expect(isNumber('ab1c23')).toBe(true);
      expect(isNumber('1')).toBe(true);
      expect(isNumber('0')).toBe(true);
      expect(isNumber('9')).toBe(true);
    });

    test('returns false for strings without numbers', () => {
      expect(isNumber('abc')).toBe(false);
      expect(isNumber('ABC')).toBe(false);
      expect(isNumber('!@#$%')).toBe(false);
      expect(isNumber('')).toBe(false);
      expect(isNumber('   ')).toBe(false);
    });
  });

  describe('isLowercaseChar', () => {
    test('returns true for strings containing lowercase letters', () => {
      expect(isLowercaseChar('ABC123a')).toBe(true);
      expect(isLowercaseChar('abc')).toBe(true);
      expect(isLowercaseChar('A1b')).toBe(true);
      expect(isLowercaseChar('z')).toBe(true);
      expect(isLowercaseChar('!@#a')).toBe(true);
    });

    test('returns false for strings without lowercase letters', () => {
      expect(isLowercaseChar('ABC')).toBe(false);
      expect(isLowercaseChar('123')).toBe(false);
      expect(isLowercaseChar('!@#$%')).toBe(false);
      expect(isLowercaseChar('')).toBe(false);
      expect(isLowercaseChar('   ')).toBe(false);
    });
  });

  describe('isUppercaseChar', () => {
    test('returns true for strings containing uppercase letters', () => {
      expect(isUppercaseChar('abc123A')).toBe(true);
      expect(isUppercaseChar('ABC')).toBe(true);
      expect(isUppercaseChar('a1B')).toBe(true);
      expect(isUppercaseChar('Z')).toBe(true);
      expect(isUppercaseChar('!@#A')).toBe(true);
    });

    test('returns false for strings without uppercase letters', () => {
      expect(isUppercaseChar('abc')).toBe(false);
      expect(isUppercaseChar('123')).toBe(false);
      expect(isUppercaseChar('!@#$%')).toBe(false);
      expect(isUppercaseChar('')).toBe(false);
      expect(isUppercaseChar('   ')).toBe(false);
    });
  });

  describe('isSpecialChar', () => {
    test('returns true for strings containing special characters', () => {
      expect(isSpecialChar('abc123!')).toBe(true);
      expect(isSpecialChar('abc@123')).toBe(true);
      expect(isSpecialChar('#password')).toBe(true);
      expect(isSpecialChar('test$')).toBe(true);
      expect(isSpecialChar('pass%word')).toBe(true);
      expect(isSpecialChar('test^123')).toBe(true);
      expect(isSpecialChar('pass&word')).toBe(true);
      expect(isSpecialChar('test*123')).toBe(true);
      expect(isSpecialChar('pass.word')).toBe(true);
      expect(isSpecialChar('test,123')).toBe(true);
      expect(isSpecialChar('pass?word')).toBe(true);
      expect(isSpecialChar('test-123')).toBe(true);
      expect(isSpecialChar('pass+word')).toBe(true);
      expect(isSpecialChar('test_123')).toBe(true);
    });

    test('returns false for strings without special characters', () => {
      expect(isSpecialChar('abc')).toBe(false);
      expect(isSpecialChar('ABC')).toBe(false);
      expect(isSpecialChar('123')).toBe(false);
      expect(isSpecialChar('abc123')).toBe(false);
      expect(isSpecialChar('ABC123')).toBe(false);
      expect(isSpecialChar('')).toBe(false);
      expect(isSpecialChar('   ')).toBe(false);
    });
  });

  describe('minLength', () => {
    test('returns true for passwords longer than 7 characters', () => {
      expect(minLength('12345678')).toBe(true);
      expect(minLength('password123')).toBe(true);
      expect(minLength('a'.repeat(8))).toBe(true);
      expect(minLength('a'.repeat(100))).toBe(true);
    });

    test('returns false for passwords 7 characters or shorter', () => {
      expect(minLength('')).toBe(false);
      expect(minLength('1')).toBe(false);
      expect(minLength('12')).toBe(false);
      expect(minLength('123')).toBe(false);
      expect(minLength('1234')).toBe(false);
      expect(minLength('12345')).toBe(false);
      expect(minLength('123456')).toBe(false);
      expect(minLength('1234567')).toBe(false);
    });
  });

  describe('integration tests', () => {
    test('validates strong password', () => {
      const strongPassword = 'StrongPass123!';
      expect(isNumber(strongPassword)).toBe(true);
      expect(isLowercaseChar(strongPassword)).toBe(true);
      expect(isUppercaseChar(strongPassword)).toBe(true);
      expect(isSpecialChar(strongPassword)).toBe(true);
      expect(minLength(strongPassword)).toBe(true);
    });

    test('validates weak password', () => {
      const weakPassword = 'weak';
      expect(isNumber(weakPassword)).toBe(false);
      expect(isLowercaseChar(weakPassword)).toBe(true);
      expect(isUppercaseChar(weakPassword)).toBe(false);
      expect(isSpecialChar(weakPassword)).toBe(false);
      expect(minLength(weakPassword)).toBe(false);
    });
  });
});
