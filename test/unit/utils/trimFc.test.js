const trimFc = require('../../../src/utils/trimFc').default;

describe('trimFc utility', () => {
  let mockFormik;
  let mockEvent;

  beforeEach(() => {
    mockFormik = {
      setFieldValue: jest.fn()
    };
    
    mockEvent = {
      target: {
        name: 'testField',
        value: ''
      }
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('basic trimming functionality', () => {
    test('trims leading whitespace', () => {
      mockEvent.target.value = '   hello world';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello world');
    });

    test('trims trailing whitespace but leaves one space', () => {
      mockEvent.target.value = 'hello world   ';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello world ');
    });

    test('trims both leading and trailing whitespace', () => {
      mockEvent.target.value = '   hello world   ';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello world ');
    });

    test('handles empty string', () => {
      mockEvent.target.value = '';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', '');
    });

    test('handles string with only whitespace', () => {
      mockEvent.target.value = '   ';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      // ltrim removes leading spaces, rtrim on empty string returns empty string
      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', '');
    });

    test('handles string with no whitespace', () => {
      mockEvent.target.value = 'hello';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello');
    });
  });

  describe('edge cases', () => {
    test('handles null value', () => {
      mockEvent.target.value = null;
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', null);
    });

    test('handles undefined value', () => {
      mockEvent.target.value = undefined;
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', undefined);
    });

    test('handles multiple consecutive spaces', () => {
      mockEvent.target.value = '     hello     world     ';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello     world ');
    });

    test('handles tabs and newlines', () => {
      mockEvent.target.value = '\t\n  hello world  \t\n';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('testField', 'hello world ');
    });
  });

  describe('formik integration', () => {
    test('calls setFieldValue with correct field name', () => {
      mockEvent.target.name = 'customFieldName';
      mockEvent.target.value = '  test  ';
      const trimFunction = trimFc(mockFormik);
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledWith('customFieldName', 'test ');
    });

    test('works with different field names', () => {
      const testCases = [
        { name: 'firstName', value: '  John  ' },
        { name: 'lastName', value: '  Doe  ' },
        { name: 'email', value: '  <EMAIL>  ' }
      ];

      testCases.forEach(({ name, value }) => {
        mockEvent.target.name = name;
        mockEvent.target.value = value;
        const trimFunction = trimFc(mockFormik);
        trimFunction(mockEvent);

        expect(mockFormik.setFieldValue).toHaveBeenCalledWith(name, value.replace(/^\s+/g, '').replace(/\s+$/g, ' '));
      });
    });
  });

  describe('return value', () => {
    test('returns a function', () => {
      const result = trimFc(mockFormik);
      expect(typeof result).toBe('function');
    });

    test('returned function can be called multiple times', () => {
      const trimFunction = trimFc(mockFormik);
      
      mockEvent.target.value = '  first  ';
      trimFunction(mockEvent);
      
      mockEvent.target.value = '  second  ';
      trimFunction(mockEvent);

      expect(mockFormik.setFieldValue).toHaveBeenCalledTimes(2);
      expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(1, 'testField', 'first ');
      expect(mockFormik.setFieldValue).toHaveBeenNthCalledWith(2, 'testField', 'second ');
    });
  });
});
