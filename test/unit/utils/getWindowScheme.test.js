const getWindowScheme = require('../../../src/utils/getWindowScheme').default;

describe('getWindowScheme utility', () => {
  let mockMatchMedia;

  beforeEach(() => {
    // Mock window.matchMedia
    mockMatchMedia = jest.fn();
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('dark mode detection', () => {
    test('returns true when user prefers dark mode', () => {
      mockMatchMedia.mockReturnValue({
        matches: true
      });

      const result = getWindowScheme();

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
      expect(result).toBe(true);
    });

    test('returns false when user prefers light mode', () => {
      mockMatchMedia.mockReturnValue({
        matches: false
      });

      const result = getWindowScheme();

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
      expect(result).toBe(false);
    });
  });

  describe('media query handling', () => {
    test('calls matchMedia with correct query', () => {
      mockMatchMedia.mockReturnValue({
        matches: false
      });

      getWindowScheme();

      expect(mockMatchMedia).toHaveBeenCalledTimes(1);
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    });

    test('handles matchMedia returning null', () => {
      mockMatchMedia.mockReturnValue(null);

      expect(() => getWindowScheme()).toThrow();
    });

    test('handles matchMedia returning undefined', () => {
      mockMatchMedia.mockReturnValue(undefined);

      expect(() => getWindowScheme()).toThrow();
    });
  });

  describe('browser compatibility', () => {
    test('handles browsers without matchMedia support', () => {
      // Remove matchMedia from window
      delete window.matchMedia;

      expect(() => getWindowScheme()).toThrow();
    });

    test('handles matchMedia with additional properties', () => {
      mockMatchMedia.mockReturnValue({
        matches: true,
        media: '(prefers-color-scheme: dark)',
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
      });

      const result = getWindowScheme();

      expect(result).toBe(true);
    });
  });

  describe('multiple calls', () => {
    test('calls matchMedia on each invocation', () => {
      mockMatchMedia.mockReturnValue({
        matches: false
      });

      getWindowScheme();
      getWindowScheme();
      getWindowScheme();

      expect(mockMatchMedia).toHaveBeenCalledTimes(3);
    });

    test('can return different values on subsequent calls', () => {
      // First call returns false (light mode)
      mockMatchMedia.mockReturnValueOnce({
        matches: false
      });

      // Second call returns true (dark mode)
      mockMatchMedia.mockReturnValueOnce({
        matches: true
      });

      expect(getWindowScheme()).toBe(false);
      expect(getWindowScheme()).toBe(true);
    });
  });

  describe('edge cases', () => {
    test('handles matchMedia throwing an error', () => {
      mockMatchMedia.mockImplementation(() => {
        throw new Error('matchMedia not supported');
      });

      expect(() => getWindowScheme()).toThrow('matchMedia not supported');
    });

    test('handles matchMedia returning object without matches property', () => {
      mockMatchMedia.mockReturnValue({
        media: '(prefers-color-scheme: dark)'
        // missing matches property
      });

      const result = getWindowScheme();

      expect(result).toBe(undefined);
    });

    test('handles matchMedia returning object with non-boolean matches', () => {
      mockMatchMedia.mockReturnValue({
        matches: 'true' // string instead of boolean
      });

      const result = getWindowScheme();

      expect(result).toBe('true');
    });
  });

  describe('integration scenarios', () => {
    test('simulates system theme change', () => {
      // Initially light mode
      mockMatchMedia.mockReturnValueOnce({
        matches: false
      });

      expect(getWindowScheme()).toBe(false);

      // System changes to dark mode
      mockMatchMedia.mockReturnValueOnce({
        matches: true
      });

      expect(getWindowScheme()).toBe(true);
    });

    test('works with real-world matchMedia response structure', () => {
      // Simulate a real MediaQueryList object
      const mockMediaQueryList = {
        matches: true,
        media: '(prefers-color-scheme: dark)',
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
      };

      mockMatchMedia.mockReturnValue(mockMediaQueryList);

      const result = getWindowScheme();

      expect(result).toBe(true);
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    });
  });
});
