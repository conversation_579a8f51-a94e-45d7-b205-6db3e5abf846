import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../test-utils';

// Mock all the dependencies first
jest.mock('hooks/useAuth');
jest.mock('hooks/useScriptRef');
jest.mock('contexts/JWTContext', () => {
  const mockReact = require('react');
  return {
    __esModule: true,
    default: mockReact.createContext()
  };
});

// Mock utils/axios to prevent import.meta issues
jest.mock('utils/axios', () => ({
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  defaults: {
    headers: {
      common: {}
    }
  }
}));

// Now import the component after mocking dependencies
import AuthLogin from '../../../src/sections/auth/auth-forms/AuthLogin';

// Mock the components
jest.mock('components/@extended/IconButton', () => {
  return function MockIconButton({ children, onClick, ...props }) {
    return (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('components/@extended/AnimateButton', () => {
  return function MockAnimateButton({ children }) {
    return <div>{children}</div>;
  };
});

// Mock the icons
jest.mock('iconsax-react', () => ({
  Eye: () => <span data-testid="eye-icon">Eye</span>,
  EyeSlash: () => <span data-testid="eye-slash-icon">EyeSlash</span>
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Link: ({ children, to, ...props }) => (
    <a href={to} {...props}>
      {children}
    </a>
  )
}));

describe('AuthLogin Component - Final Tests', () => {
  let mockLogin;
  let mockScriptRef;
  let useAuth;
  let useScriptRef;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock functions
    mockLogin = jest.fn();
    mockScriptRef = { current: true };

    // Mock the hooks
    useAuth = require('hooks/useAuth').default;
    useScriptRef = require('hooks/useScriptRef').default;

    useAuth.mockReturnValue({
      isLoggedIn: false,
      login: mockLogin
    });

    useScriptRef.mockReturnValue(mockScriptRef);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    test('renders login form with basic elements', () => {
      render(<AuthLogin />);

      // Check for form elements using more specific selectors
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /toggle password visibility/i })).toBeInTheDocument();

      // Check for links
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();

      // Check for checkbox
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
    });

    test('renders with forgot password prop', () => {
      render(<AuthLogin forgot="/forgot-password" />);

      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
    });

    test('renders email input with correct attributes', () => {
      render(<AuthLogin />);

      const emailInput = screen.getByLabelText(/email address/i);
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(emailInput).toHaveAttribute('name', 'username');
    });

    test('renders password input with correct attributes', () => {
      render(<AuthLogin />);

      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(passwordInput).toHaveAttribute('name', 'password');
    });
  });

  describe('User Input Handling', () => {
    test('handles email input changes', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const emailInput = screen.getByLabelText(/email address/i);
      await user.type(emailInput, '<EMAIL>');

      expect(emailInput).toHaveValue('<EMAIL>');
    });

    test('handles password input changes', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      await user.type(passwordInput, 'password123');

      expect(passwordInput).toHaveValue('password123');
    });

    test('handles checkbox state changes', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).not.toBeChecked();

      await user.click(checkbox);
      expect(checkbox).toBeChecked();

      await user.click(checkbox);
      expect(checkbox).not.toBeChecked();
    });
  });

  describe('Password Visibility Toggle', () => {
    test('toggles password visibility when eye icon is clicked', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

      // Initially password should be hidden
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(screen.getByTestId('eye-slash-icon')).toBeInTheDocument();

      // Click to show password
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
      expect(screen.getByTestId('eye-icon')).toBeInTheDocument();

      // Click to hide password again
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(screen.getByTestId('eye-slash-icon')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    test('shows validation errors for empty fields', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const loginButton = screen.getByRole('button', { name: /login/i });

      // Try to submit empty form
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });

    test('form validation works correctly', async () => {
      const user = userEvent.setup();
      render(<AuthLogin />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      // Type invalid email
      await user.type(emailInput, 'invalid-email');
      await user.tab();

      // Button should be disabled for invalid form
      await waitFor(() => {
        expect(loginButton).toBeDisabled();
      });

      // Clear and type valid email
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Form should now have valid values
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(passwordInput).toHaveValue('password123');
    });
  });

  describe('Form Submission', () => {
    test('calls login function with correct credentials on form submission', async () => {
      const user = userEvent.setup();
      mockLogin.mockResolvedValue();

      render(<AuthLogin />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const form = emailInput.closest('form');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Submit the form directly
      fireEvent.submit(form);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });
    });
  });

  describe('Error Handling', () => {
    test('displays error message when login fails', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Invalid credentials';
      mockLogin.mockRejectedValue(new Error(errorMessage));

      render(<AuthLogin />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const form = emailInput.closest('form');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrong-password');

      fireEvent.submit(form);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', () => {
      render(<AuthLogin />);

      // Check for proper labels
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();

      // Check for proper roles
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /toggle password visibility/i })).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();
    });

    test('password toggle button has proper aria-label', () => {
      render(<AuthLogin />);

      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });
      expect(toggleButton).toHaveAttribute('aria-label', 'toggle password visibility');
    });
  });

  describe('Props and Context Integration', () => {
    test('works with different auth context states', () => {
      const customAuthContext = {
        isLoggedIn: true,
        login: mockLogin
      };

      useAuth.mockReturnValue(customAuthContext);

      render(<AuthLogin />);

      // Component should still render properly
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    });

    test('handles forgot prop correctly', () => {
      const forgotPath = '/custom-forgot-password';
      render(<AuthLogin forgot={forgotPath} />);

      // Component should render with the forgot prop
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    });
  });
});
