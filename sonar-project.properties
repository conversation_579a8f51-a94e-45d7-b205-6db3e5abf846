sonar.projectKey=ats-frontend-application
sonar.projectName=ATS Frontend Application
sonar.projectVersion=9.2.0
sonar.host.url=http://localhost:9000
# sonar.token should be set via environment variable SONAR_TOKEN
# or passed as a command line parameter with -Dsonar.login=
# sonar.token=
sonar.token=sqp_f1cf8051bd3ad10bafcc5cc9b83e03ad103d048a
sonar.sources=src
sonar.inclusions=src/pages/userPermissions/roles/**,src/pages/userPermissions/users/**,src/sections/auth/auth-forms/AuthLogin.jsx,src/sections/auth/auth-forms/AuthForgotPassword.jsx,src/sections/auth/auth-forms/AuthCreatePassword.jsx,src/sections/auth/auth-forms/AuthLogout.jsx,src/sections/auth/auth-forms/AuthRegister.jsx,src/sections/auth/auth-forms/AuthResetPassword.jsx
sonar.tests=test
sonar.test.inclusions=test/unit/**/*.test.js,test/unit/**/*.test.jsx,test/integration/**/*.test.js,test/integration/**/*.test.jsx
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.sourceEncoding=UTF-8
sonar.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/coverage/**/*,**/node_modules/**/*,**/*.d.ts,**/src/index.jsx,**/src/config.js,**/src/setupTests.js,**/src/reportWebVitals.js,**/src/vite-env.d.js,**/src/mocks/**/*,**/src/data/**/*,**/public/**/*,**/docs/**/*,**/examples/**/*,**/scripts/**/*
sonar.coverage.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*,**/src/mocks/**/*,**/src/data/**/*